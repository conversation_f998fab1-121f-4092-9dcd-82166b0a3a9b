import { paraglideVitePlugin } from '@inlang/paraglide-js';
import { cloudflare } from '@cloudflare/vite-plugin';
import { reactRouter } from '@react-router/dev/vite';
import tsconfigPaths from 'vite-tsconfig-paths';
import oxlintPlugin from 'vite-plugin-oxlint';
import babel from 'vite-plugin-babel';
import tailwindcss from 'tailwindcss';
import { defineConfig } from 'vite';
import dedent from 'dedent';

const ReactCompilerConfig = {
  /* ... */
};

export default defineConfig({
  plugins: [
    oxlintPlugin(),
    reactRouter(),
    cloudflare(),
    babel({
      filter: /\.[jt]sx?$/,
      babelConfig: {
        presets: ['@babel/preset-typescript'], // if you use TypeScript
        plugins: [['babel-plugin-react-compiler', ReactCompilerConfig]],
      },
    }),
    tsconfigPaths(),
    {
      name: 'add-headers',
      applyToEnvironment: (env) => env.name === 'client',
      generateBundle() {
        this.emitFile({
          fileName: '_headers',
          type: 'asset',
          source: dedent`
            # Autogenerated

            /assets/*
              ! Cache-Control
                Cache-Control: public, immutable, max-age=31536000
          `,
        });
      },
    },
    paraglideVitePlugin({
      project: './project.inlang',
      outdir: './paraglide',
      strategy: ['cookie', 'baseLocale'],
    }),
  ],
  server: {
    port: 3000,
    warmup: {
      clientFiles: ['./app/**/*', './components/**/*'],
    },
  },
  css: {
    postcss: {
      plugins: [tailwindcss()],
    },
  },
  //   ssr: {
  //     optimizeDeps: {
  //       include: ['novel', '@tiptap/extension-placeholder'],
  //     },
  //   },
  build: {
    sourcemap: false,
  },
  resolve: {
    alias: {
      tslib: 'tslib/tslib.es6.js',
    },
  },
});
