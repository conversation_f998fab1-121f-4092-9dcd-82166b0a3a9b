import { supabase } from '@/lib/supabase';
import type { Database } from '@/lib/database.types';

type Tables = Database['public']['Tables'];
type User = Tables['users']['Row'];
type Connection = Tables['connections']['Row'];
type Note = Tables['notes']['Row'];
type Summary = Tables['summaries']['Row'];
type UserSettings = Tables['user_settings']['Row'];
type UserHotkeys = Tables['user_hotkeys']['Row'];

export class DatabaseService {
  // User operations
  static async getUser(userId: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) throw error;
    return data;
  }

  static async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteUser(userId: string): Promise<void> {
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', userId);

    if (error) throw error;
  }

  // Connection operations
  static async getUserConnections(userId: string): Promise<Connection[]> {
    const { data, error } = await supabase
      .from('connections')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getConnection(connectionId: string): Promise<Connection | null> {
    const { data, error } = await supabase
      .from('connections')
      .select('*')
      .eq('id', connectionId)
      .single();

    if (error) throw error;
    return data;
  }

  static async createConnection(connection: Tables['connections']['Insert']): Promise<Connection> {
    const { data, error } = await supabase
      .from('connections')
      .insert(connection)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateConnection(connectionId: string, updates: Partial<Connection>): Promise<Connection> {
    const { data, error } = await supabase
      .from('connections')
      .update(updates)
      .eq('id', connectionId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteConnection(connectionId: string): Promise<void> {
    const { error } = await supabase
      .from('connections')
      .delete()
      .eq('id', connectionId);

    if (error) throw error;
  }

  // Notes operations
  static async getNotesByThreadId(userId: string, threadId: string): Promise<Note[]> {
    const { data, error } = await supabase
      .from('notes')
      .select('*')
      .eq('user_id', userId)
      .eq('thread_id', threadId)
      .order('is_pinned', { ascending: false })
      .order('order', { ascending: true })
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createNote(note: Tables['notes']['Insert']): Promise<Note> {
    const { data, error } = await supabase
      .from('notes')
      .insert(note)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateNote(noteId: string, updates: Partial<Note>): Promise<Note> {
    const { data, error } = await supabase
      .from('notes')
      .update(updates)
      .eq('id', noteId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteNote(noteId: string): Promise<void> {
    const { error } = await supabase
      .from('notes')
      .delete()
      .eq('id', noteId);

    if (error) throw error;
  }

  static async updateManyNotes(
    userId: string,
    notes: { id: string; order: number; is_pinned?: boolean }[]
  ): Promise<void> {
    const updates = notes.map(note => ({
      id: note.id,
      order: note.order,
      is_pinned: note.is_pinned,
      updated_at: new Date().toISOString(),
    }));

    for (const update of updates) {
      const { error } = await supabase
        .from('notes')
        .update(update)
        .eq('id', update.id)
        .eq('user_id', userId);

      if (error) throw error;
    }
  }

  // User settings operations
  static async getUserSettings(userId: string): Promise<UserSettings | null> {
    const { data, error } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "not found"
    return data;
  }

  static async updateUserSettings(userId: string, settings: any): Promise<UserSettings> {
    const { data, error } = await supabase
      .from('user_settings')
      .upsert({
        user_id: userId,
        settings,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // User hotkeys operations
  static async getUserHotkeys(userId: string): Promise<UserHotkeys | null> {
    const { data, error } = await supabase
      .from('user_hotkeys')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  static async updateUserHotkeys(userId: string, shortcuts: any): Promise<UserHotkeys> {
    const { data, error } = await supabase
      .from('user_hotkeys')
      .upsert({
        user_id: userId,
        shortcuts,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // Summary operations
  static async getSummary(messageId: string): Promise<Summary | null> {
    const { data, error } = await supabase
      .from('summaries')
      .select('*')
      .eq('message_id', messageId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  static async createSummary(summary: Tables['summaries']['Insert']): Promise<Summary> {
    const { data, error } = await supabase
      .from('summaries')
      .insert(summary)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateSummary(messageId: string, updates: Partial<Summary>): Promise<Summary> {
    const { data, error } = await supabase
      .from('summaries')
      .update(updates)
      .eq('message_id', messageId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
}
