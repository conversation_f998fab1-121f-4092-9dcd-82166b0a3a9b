import { supabase } from '@/lib/supabase';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface EmailProcessingRequest {
  messageId: string;
  connectionId: string;
  emailContent: string;
  subject: string;
  sender: string;
}

export class AIService {
  /**
   * Send a chat message to the AI and get a streaming response
   */
  static async chat(messages: ChatMessage[], model = 'gemini-1.5-flash'): Promise<Response> {
    const { data, error } = await supabase.functions.invoke('ai-chat', {
      body: { messages, model },
    });

    if (error) throw error;
    return data;
  }

  /**
   * Process an email to generate summary and suggested reply
   */
  static async processEmail(request: EmailProcessingRequest) {
    const { data, error } = await supabase.functions.invoke('email-processor', {
      body: request,
    });

    if (error) throw error;
    return data;
  }

  /**
   * Generate a summary for any text content
   */
  static async generateSummary(content: string, context?: string): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'You are an AI assistant that creates concise, helpful summaries. Provide a clear summary highlighting key points and important information. Keep it under 200 words.',
      },
      {
        role: 'user',
        content: context ? `Context: ${context}\n\nContent to summarize:\n${content}` : content,
      },
    ];

    const response = await this.chat(messages);
    
    // For non-streaming response, you'd parse the response here
    // This is a simplified version - you might want to handle streaming
    const result = await response.json();
    return result.content || 'Unable to generate summary';
  }

  /**
   * Generate a reply suggestion for an email
   */
  static async generateReply(
    emailContent: string,
    subject: string,
    sender: string,
    tone: 'professional' | 'casual' | 'friendly' = 'professional'
  ): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: `You are an AI assistant that suggests email replies. Generate a ${tone}, appropriate reply to the email. Keep it concise and helpful.`,
      },
      {
        role: 'user',
        content: `Subject: ${subject}\nFrom: ${sender}\n\nEmail Content:\n${emailContent}`,
      },
    ];

    const response = await this.chat(messages);
    
    // For non-streaming response
    const result = await response.json();
    return result.content || 'Unable to generate reply';
  }
}
