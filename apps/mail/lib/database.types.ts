// This file will be auto-generated by Supabase CLI
// Run: supabase gen types typescript --project-id YOUR_PROJECT_ID > lib/database.types.ts

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      // User management
      users: {
        Row: {
          id: string
          name: string
          email: string
          email_verified: boolean
          image: string | null
          created_at: string
          updated_at: string
          default_connection_id: string | null
          custom_prompt: string | null
          phone_number: string | null
          phone_number_verified: boolean | null
        }
        Insert: {
          id?: string
          name: string
          email: string
          email_verified?: boolean
          image?: string | null
          created_at?: string
          updated_at?: string
          default_connection_id?: string | null
          custom_prompt?: string | null
          phone_number?: string | null
          phone_number_verified?: boolean | null
        }
        Update: {
          id?: string
          name?: string
          email?: string
          email_verified?: boolean
          image?: string | null
          created_at?: string
          updated_at?: string
          default_connection_id?: string | null
          custom_prompt?: string | null
          phone_number?: string | null
          phone_number_verified?: boolean | null
        }
      }
      
      // Email connections
      connections: {
        Row: {
          id: string
          user_id: string
          email: string
          name: string | null
          picture: string | null
          access_token: string | null
          refresh_token: string | null
          scope: string
          provider_id: 'google' | 'microsoft'
          expires_at: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          name?: string | null
          picture?: string | null
          access_token?: string | null
          refresh_token?: string | null
          scope: string
          provider_id: 'google' | 'microsoft'
          expires_at: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          name?: string | null
          picture?: string | null
          access_token?: string | null
          refresh_token?: string | null
          scope?: string
          provider_id?: 'google' | 'microsoft'
          expires_at?: string
          created_at?: string
          updated_at?: string
        }
      }
      
      // Notes
      notes: {
        Row: {
          id: string
          user_id: string
          thread_id: string
          content: string
          color: string
          is_pinned: boolean | null
          order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          thread_id: string
          content: string
          color?: string
          is_pinned?: boolean | null
          order?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          thread_id?: string
          content?: string
          color?: string
          is_pinned?: boolean | null
          order?: number
          created_at?: string
          updated_at?: string
        }
      }
      
      // Email summaries
      summaries: {
        Row: {
          message_id: string
          content: string
          created_at: string
          updated_at: string
          connection_id: string
          saved: boolean
          tags: string | null
          suggested_reply: string | null
        }
        Insert: {
          message_id: string
          content: string
          created_at?: string
          updated_at?: string
          connection_id: string
          saved?: boolean
          tags?: string | null
          suggested_reply?: string | null
        }
        Update: {
          message_id?: string
          content?: string
          created_at?: string
          updated_at?: string
          connection_id?: string
          saved?: boolean
          tags?: string | null
          suggested_reply?: string | null
        }
      }
      
      // User settings
      user_settings: {
        Row: {
          id: string
          user_id: string
          settings: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          settings: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          settings?: Json
          created_at?: string
          updated_at?: string
        }
      }
      
      // User hotkeys
      user_hotkeys: {
        Row: {
          user_id: string
          shortcuts: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
          shortcuts: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          user_id?: string
          shortcuts?: Json
          created_at?: string
          updated_at?: string
        }
      }
      
      // Writing style matrix
      writing_style_matrix: {
        Row: {
          connection_id: string
          num_messages: number
          style: Json
          updated_at: string
        }
        Insert: {
          connection_id: string
          num_messages: number
          style: Json
          updated_at?: string
        }
        Update: {
          connection_id?: string
          num_messages?: number
          style?: Json
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      provider_type: 'google' | 'microsoft'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
