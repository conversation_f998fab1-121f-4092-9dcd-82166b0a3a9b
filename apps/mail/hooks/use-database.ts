import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DatabaseService } from '@/lib/services/database';
import { useSupabase } from '@/providers/supabase-provider';
import type { Database } from '@/lib/database.types';

type Tables = Database['public']['Tables'];
type User = Tables['users']['Row'];
type Connection = Tables['connections']['Row'];
type Note = Tables['notes']['Row'];
type UserSettings = Tables['user_settings']['Row'];

// Query keys
export const queryKeys = {
  user: (userId: string) => ['user', userId],
  connections: (userId: string) => ['connections', userId],
  connection: (connectionId: string) => ['connection', connectionId],
  notes: (userId: string, threadId: string) => ['notes', userId, threadId],
  userSettings: (userId: string) => ['userSettings', userId],
  userHotkeys: (userId: string) => ['userHotkeys', userId],
  summary: (messageId: string) => ['summary', messageId],
};

// User hooks
export function useUser() {
  const { user } = useSupabase();
  
  return useQuery({
    queryKey: queryKeys.user(user?.id || ''),
    queryFn: () => DatabaseService.getUser(user!.id),
    enabled: !!user?.id,
  });
}

export function useUpdateUser() {
  const { user } = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (updates: Partial<User>) => 
      DatabaseService.updateUser(user!.id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.user(user!.id) });
    },
  });
}

// Connection hooks
export function useConnections() {
  const { user } = useSupabase();
  
  return useQuery({
    queryKey: queryKeys.connections(user?.id || ''),
    queryFn: () => DatabaseService.getUserConnections(user!.id),
    enabled: !!user?.id,
  });
}

export function useConnection(connectionId: string) {
  return useQuery({
    queryKey: queryKeys.connection(connectionId),
    queryFn: () => DatabaseService.getConnection(connectionId),
    enabled: !!connectionId,
  });
}

export function useCreateConnection() {
  const { user } = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (connection: Tables['connections']['Insert']) =>
      DatabaseService.createConnection(connection),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.connections(user!.id) });
    },
  });
}

export function useUpdateConnection() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ connectionId, updates }: { connectionId: string; updates: Partial<Connection> }) =>
      DatabaseService.updateConnection(connectionId, updates),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.connection(data.id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.connections(data.user_id) });
    },
  });
}

export function useDeleteConnection() {
  const { user } = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (connectionId: string) => DatabaseService.deleteConnection(connectionId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.connections(user!.id) });
    },
  });
}

// Notes hooks
export function useNotes(threadId: string) {
  const { user } = useSupabase();
  
  return useQuery({
    queryKey: queryKeys.notes(user?.id || '', threadId),
    queryFn: () => DatabaseService.getNotesByThreadId(user!.id, threadId),
    enabled: !!user?.id && !!threadId,
  });
}

export function useCreateNote() {
  const { user } = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (note: Tables['notes']['Insert']) => DatabaseService.createNote(note),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.notes(user!.id, data.thread_id) 
      });
    },
  });
}

export function useUpdateNote() {
  const { user } = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ noteId, updates }: { noteId: string; updates: Partial<Note> }) =>
      DatabaseService.updateNote(noteId, updates),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.notes(user!.id, data.thread_id) 
      });
    },
  });
}

export function useDeleteNote() {
  const { user } = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ noteId, threadId }: { noteId: string; threadId: string }) => 
      DatabaseService.deleteNote(noteId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.notes(user!.id, variables.threadId) 
      });
    },
  });
}

export function useUpdateManyNotes() {
  const { user } = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      notes, 
      threadId 
    }: { 
      notes: { id: string; order: number; is_pinned?: boolean }[]; 
      threadId: string;
    }) => DatabaseService.updateManyNotes(user!.id, notes),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.notes(user!.id, variables.threadId) 
      });
    },
  });
}

// Settings hooks
export function useUserSettings() {
  const { user } = useSupabase();
  
  return useQuery({
    queryKey: queryKeys.userSettings(user?.id || ''),
    queryFn: () => DatabaseService.getUserSettings(user!.id),
    enabled: !!user?.id,
  });
}

export function useUpdateUserSettings() {
  const { user } = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: any) => DatabaseService.updateUserSettings(user!.id, settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.userSettings(user!.id) });
    },
  });
}

// Hotkeys hooks
export function useUserHotkeys() {
  const { user } = useSupabase();
  
  return useQuery({
    queryKey: queryKeys.userHotkeys(user?.id || ''),
    queryFn: () => DatabaseService.getUserHotkeys(user!.id),
    enabled: !!user?.id,
  });
}

export function useUpdateUserHotkeys() {
  const { user } = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (shortcuts: any) => DatabaseService.updateUserHotkeys(user!.id, shortcuts),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.userHotkeys(user!.id) });
    },
  });
}
