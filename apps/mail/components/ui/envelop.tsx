export default function Envelop() {
  return (
    <div className="relative h-[303.33px] w-[455px]">
      <div className="absolute left-0 h-[303.33px] w-[455px] rounded-[28.61px] border-4 border-slate-600 bg-gradient-to-b from-[#eeeff1] to-[#f9f9fa] backdrop-blur-[85.60px]" />
      <div className="overflow-hidden">
        <div className="absolute h-[303.33px] w-[455px] rounded-[28.61px] bg-black/10" />
        <div data-svg-wrapper className="absolute left-4 top-2">
          <svg
            width="420"
            height="300"
            viewBox="0 0 515 338"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g opacity="0.4">
              <g filter="url(#filter0_f_5652_475)">
                <path
                  d="M10.7292 337.333L504.271 337.333L288.096 143.09C277.281 133.371 271.873 128.512 265.758 126.668C260.373 125.045 254.628 125.045 249.242 126.668C243.127 128.512 237.719 133.371 226.904 143.09L10.7292 337.333Z"
                  fill="#334155"
                  fillOpacity="0.4"
                />
              </g>
              <g filter="url(#filter1_f_5652_475)">
                <path
                  d="M39.3403 337.333L475.66 337.333L291.576 132.125C279.779 118.975 273.881 112.4 266.929 109.973C260.824 107.842 254.176 107.842 248.071 109.973C241.119 112.4 235.221 118.975 223.424 132.125L39.3403 337.333Z"
                  fill="#334155"
                  fillOpacity="0.2"
                />
              </g>
              <g filter="url(#filter2_f_5652_475)">
                <path
                  d="M10.7292 337.333L504.271 337.333L290.981 108.588C279.343 96.1067 273.524 89.8661 266.721 87.5499C260.742 85.5144 254.258 85.5144 248.279 87.5499C241.476 89.8661 235.657 96.1067 224.019 108.588L10.7292 337.333Z"
                  fill="#334155"
                  fillOpacity="0.1"
                />
              </g>
            </g>
            <foreignObject x="-16" y="108.743" width="547" height="244.591">
              <div
                style={{
                  backdropFilter: 'blur(8px)',
                  clipPath: 'url(#bgblur_0_5652_475_clip_path)',
                  height: '100%',
                  width: '100%',
                }}
              ></div>
            </foreignObject>
            <path
              data-figma-bg-blur-radius="16"
              d="M0 337.334L515 337.334L287.371 141.32C276.773 132.194 271.474 127.631 265.527 125.892C260.285 124.36 254.715 124.36 249.473 125.892C243.526 127.631 238.227 132.194 227.629 141.32L0 337.334Z"
              fill="url(#paint0_linear_5652_475)"
              fillOpacity="0.4"
            />
            <path
              d="M5.36459 330.181L227.653 139.422C238.245 130.333 243.54 125.789 249.48 124.059C254.714 122.535 260.276 122.538 265.509 124.068C271.446 125.804 276.736 130.355 287.317 139.456L513.212 333.757"
              stroke="#475569"
              strokeOpacity="0.06"
              strokeWidth="3.57639"
            />
            <defs>
              <filter
                id="filter0_f_5652_475"
                x="-3.57635"
                y="111.145"
                width="522.153"
                height="240.494"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="7.15278" result="effect1_foregroundBlur_5652_475" />
              </filter>
              <filter
                id="filter1_f_5652_475"
                x="-3.57635"
                y="65.4578"
                width="522.153"
                height="314.792"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="21.4583" result="effect1_foregroundBlur_5652_475" />
              </filter>
              <filter
                id="filter2_f_5652_475"
                x="-75.1041"
                y="0.189957"
                width="665.208"
                height="422.977"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="42.9167" result="effect1_foregroundBlur_5652_475" />
              </filter>
              <clipPath id="bgblur_0_5652_475_clip_path">
                <path
                  transform="translate(16 -108.743)"
                  d="M0 337.334L515 337.334L287.371 141.32C276.773 132.194 271.474 127.631 265.527 125.892C260.285 124.36 254.715 124.36 249.473 125.892C243.526 127.631 238.227 132.194 227.629 141.32L0 337.334Z"
                />
              </clipPath>
              <linearGradient
                id="paint0_linear_5652_475"
                x1="257.5"
                y1="115.597"
                x2="257.5"
                y2="337.333"
                gradientUnits="userSpaceOnUse"
              >
                <stop offset="0.524194" stopColor="#F4F5F6" />
                <stop offset="1" stopColor="#EEF0F1" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        <div data-svg-wrapper className="absolute -top-1 left-4">
          <svg
            width="420"
            height="300"
            viewBox="0 0 515 338"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g style={{ mixBlendMode: 'multiply' }} opacity="0.7">
              <g filter="url(#filter0_f_5652_482)">
                <path
                  d="M10.7291 -6.10352e-05L504.271 -6.10352e-05L288.096 194.244C277.28 203.962 271.873 208.822 265.758 210.665C260.372 212.288 254.627 212.288 249.242 210.665C243.127 208.822 237.719 203.962 226.903 194.244L10.7291 -6.10352e-05Z"
                  fill="#334155"
                  fillOpacity="0.4"
                />
              </g>
              <g filter="url(#filter1_f_5652_482)">
                <path
                  d="M39.3402 -6.10352e-05L475.66 -6.10352e-05L291.576 205.208C279.779 218.358 273.881 224.934 266.929 227.36C260.824 229.492 254.176 229.492 248.071 227.36C241.119 224.934 235.221 218.358 223.424 205.208L39.3402 -6.10352e-05Z"
                  fill="#334155"
                  fillOpacity="0.2"
                />
              </g>
              <g filter="url(#filter2_f_5652_482)">
                <path
                  d="M10.7291 -6.10352e-05L504.271 -6.10352e-05L290.981 228.745C279.343 241.227 273.524 247.467 266.721 249.783C260.742 251.819 254.258 251.819 248.279 249.783C241.476 247.467 235.657 241.227 224.019 228.745L10.7291 -6.10352e-05Z"
                  fill="#334155"
                  fillOpacity="0.1"
                />
              </g>
              <g filter="url(#filter3_f_5652_482)">
                <path
                  d="M28.6111 -6.10352e-05L486.389 -6.10352e-05L273.337 233.026C264.829 242.331 250.171 242.331 241.663 233.026L28.6111 -6.10352e-05Z"
                  fill="#334155"
                  fillOpacity="0.05"
                />
              </g>
              <g filter="url(#filter4_f_5652_482)">
                <path
                  d="M7.15278 -3.57642L507.847 -3.57642L272.101 215.33C263.868 222.976 251.132 222.976 242.899 215.33L7.15278 -3.57642Z"
                  fill="#334155"
                  fillOpacity="0.1"
                />
              </g>
            </g>
            <path
              d="M5.36459 7.15272L227.653 197.911C238.245 207 243.54 211.545 249.48 213.274C254.714 214.798 260.276 214.795 265.509 213.265C271.446 211.529 276.736 206.978 287.317 197.877L513.212 3.57633"
              stroke="#475569"
              strokeOpacity="0.06"
              strokeWidth="3.57639"
            />
            <foreignObject x="-16" y="-16.0001" width="547" height="244.59">
              <div
                style={{
                  backdropFilter: 'blur(8px)',
                  clipPath: 'url(#bgblur_0_5652_482_clip_path)',
                  height: '100%',
                  width: '100%',
                }}
              ></div>
            </foreignObject>
            <path
              data-figma-bg-blur-radius="16"
              d="M0 -6.10352e-05L515 -6.10352e-05L287.371 196.014C276.773 205.14 271.474 209.703 265.527 211.441C260.285 212.973 254.715 212.973 249.473 211.441C243.526 209.703 238.227 205.14 227.629 196.014L0 -6.10352e-05Z"
              fill="url(#paint0_linear_5652_482)"
              fillOpacity="0.6"
            />
            <defs>
              <filter
                id="filter0_f_5652_482"
                x="-3.57644"
                y="-14.3056"
                width="522.153"
                height="240.494"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="7.15278" result="effect1_foregroundBlur_5652_482" />
              </filter>
              <filter
                id="filter1_f_5652_482"
                x="-3.57644"
                y="-42.9167"
                width="522.153"
                height="314.792"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="21.4583" result="effect1_foregroundBlur_5652_482" />
              </filter>
              <filter
                id="filter2_f_5652_482"
                x="-75.1042"
                y="-85.8334"
                width="665.208"
                height="422.977"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="42.9167" result="effect1_foregroundBlur_5652_482" />
              </filter>
              <filter
                id="filter3_f_5652_482"
                x="-7.15284"
                y="-35.764"
                width="529.306"
                height="311.532"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="17.8819" result="effect1_foregroundBlur_5652_482" />
              </filter>
              <filter
                id="filter4_f_5652_482"
                x="-7.15278"
                y="-17.882"
                width="529.306"
                height="253.252"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="7.15278" result="effect1_foregroundBlur_5652_482" />
              </filter>
              <clipPath id="bgblur_0_5652_482_clip_path">
                <path
                  transform="translate(16 16.0001)"
                  d="M0 -6.10352e-05L515 -6.10352e-05L287.371 196.014C276.773 205.14 271.474 209.703 265.527 211.441C260.285 212.973 254.715 212.973 249.473 211.441C243.526 209.703 238.227 205.14 227.629 196.014L0 -6.10352e-05Z"
                />
              </clipPath>
              <linearGradient
                id="paint0_linear_5652_482"
                x1="257.5"
                y1="-6.10352e-05"
                x2="257.5"
                y2="221.736"
                gradientUnits="userSpaceOnUse"
              >
                <stop offset="0.25" stopColor="#FAFAFB" />
                <stop offset="1" stopColor="#F4F5F6" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        <div className="absolute left-0 top-0 h-[343.33px] w-[515px] border-8 border-[#f9fafa] opacity-40 blur-[28.61px]" />
        <div className="absolute left-0 top-0 h-[303.33px] w-[455px] rounded-[28.61px] border-4 border-white/40" />
      </div>
      <div className="absolute left-[170px] top-[32px]">
        <div className="flex flex-col font-mono text-[9px] font-semibold leading-tight text-white/70">
          <span>0 Email</span>
          <span>123 Open Source Way</span>
          <span>Freemail City, FS 98765</span>
          <span>United States</span>
        </div>
      </div>
    </div>
  );
}
