.ProseMirror {
  font-size: 15px;
  font-weight: 400;
}

/* Add placeholder styles */
/* .ProseMirror p.is-editor-empty:first-child::before {
  color: #616161;
  opacity: 0.5;
  content: 'Start your asd here';
  float: left;
  height: 0;
  pointer-events: none;
} */

/* Custom image styles */

.ProseMirror img {
  transition: filter 0.1s ease-in-out;

  &:hover {
    cursor: pointer;
    filter: brightness(90%);
  }

  &.ProseMirror-selectednode {
    outline: 3px solid #5abbf7;
    filter: brightness(90%);
  }
}

.img-placeholder {
  position: relative;

  &:before {
    content: '';
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 3px solid var(--novel-stone-200);
    border-top-color: var(--novel-stone-800);
    animation: spinning 0.6s linear infinite;
  }
}

@keyframes spinning {
  to {
    transform: rotate(360deg);
  }
}

/* Custom TODO list checkboxes – shoutout to this awesome tutorial: https://moderncss.dev/pure-css-custom-checkbox-style/ */

ul[data-type='taskList'] li > label {
  margin-right: 0.2rem;
  user-select: none;
}

@media screen and (max-width: 768px) {
  ul[data-type='taskList'] li > label {
    margin-right: 0.5rem;
  }
}

ul[data-type='taskList'] li > label input[type='checkbox'] {
  -webkit-appearance: none;
  appearance: none;
  background-color: hsl(var(--background));
  cursor: pointer;
  width: 1.2em;
  height: 1.2em;
  position: relative;
  top: 5px;
  border: 2px solid hsl(var(--border));
  margin-right: 0.3rem;
  display: grid;
  place-content: center;

  &:hover {
    background-color: hsl(var(--accent));
  }

  &:active {
    background-color: hsl(var(--accent));
  }

  &::before {
    content: '';
    width: 0.65em;
    height: 0.65em;
    transform: scale(0);
    transition: 120ms transform ease-in-out;
    box-shadow: inset 1em 1em;
    transform-origin: center;
    clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
  }

  &:checked::before {
    transform: scale(1);
  }
}

ul[data-type='taskList'] li[data-checked='true'] > div > p {
  color: var(--muted-foreground);
  text-decoration: line-through;
  text-decoration-thickness: 2px;
}

/* Overwrite tippy-box original max-width */

.tippy-box {
  max-width: 400px !important;
}

.ProseMirror:not(.dragging) .ProseMirror-selectednode {
  outline: none !important;
  background-color: var(--novel-highlight-blue);
  transition: background-color 0.2s;
  box-shadow: none;
}

/* Custom Youtube Video CSS */
iframe {
  border-radius: 4px;
  min-width: 200px;
  min-height: 200px;
  display: block;
  outline: 0px solid transparent;
}

div[data-youtube-video] > iframe {
  cursor: move;
  aspect-ratio: 16 / 9;
  width: 100%;
}

.ProseMirror-selectednode iframe {
  transition: outline 0.15s;
  outline: 6px solid #fbbf24;
}

@media only screen and (max-width: 480px) {
  div[data-youtube-video] > iframe {
    max-height: 50px;
  }
}

@media only screen and (max-width: 720px) {
  div[data-youtube-video] > iframe {
    max-height: 100px;
  }
}

/* CSS for bold coloring and highlighting issue*/
span[style] > strong {
  color: inherit;
}

mark[style] > strong {
  color: inherit;
}

/* Add specific heading styles */
.ProseMirror h1 {
  font-size: 1.5rem; /* text-2xl equivalent */
  font-weight: bold;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h2 {
  font-size: 1.25rem; /* text-xl equivalent */
  font-weight: bold;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h3 {
  font-size: 1.125rem; /* text-lg equivalent */
  font-weight: bold;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
}

/* Add these new rules to fix list placeholder issues */
.ProseMirror li .is-editor-empty::before {
  content: none !important; /* Disable placeholder in list items */
}

/* Ensure list items don't show placeholders */
.ProseMirror ul li p::before,
.ProseMirror ol li p::before {
  content: none !important;
}

/* Fix for ordered lists with placeholder */
.ProseMirror ol:has(li:first-child p.is-editor-empty) {
  position: relative;
}

/* Fix for bullet lists with placeholder */
.ProseMirror ul:has(li:first-child p.is-editor-empty) {
  position: relative;
}

/* Adjust spacing between list items */
.ProseMirror ul li,
.ProseMirror ol li {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

/* Keep the list indentation the same */
.ProseMirror ul {
  padding-left: 1.5rem;
}

.ProseMirror ol {
  padding-left: 1.5rem;
}
