.ghost-text-suggestion {
  color: #666;
  opacity: 0.7;
  pointer-events: none;
}

.suggestion-panel {
  position: absolute;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 4px;
  min-width: 200px;
}

.suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background-color: #f5f5f5;
}

.suggestion-item.selected {
  background-color: #e8f0fe;
  color: #1a73e8;
}

/* Add smooth transitions */
.suggestion-panel {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add scrollbar styling */
.suggestion-panel::-webkit-scrollbar {
  width: 8px;
}

.suggestion-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.suggestion-panel::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.suggestion-panel::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ghost-text-suggestion {
    color: rgba(255, 255, 255, 0.4);
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(255, 255, 255, 0.2) 100%
    );
    background-size: 200% 100%;
    -webkit-background-clip: text;
    background-clip: text;
  }
}
