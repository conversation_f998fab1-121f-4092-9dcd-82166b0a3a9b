export const Vercel = ({ className }: { className?: string }) => (
  <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className={className}>
    <title>Vercel</title>
    <path d="M24 22.525H0l12-21.05 12 21.05z" fill="var(--icon-color)" />
  </svg>
);

export const Gmail = ({ className }: { className?: string }) => (
  <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className={className}>
    <title>Gmail</title>
    <path
      d="M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-2.023 2.309-3.178 3.927-1.964L5.455 4.64 12 9.548l6.545-4.91 1.528-1.145C21.69 2.28 24 3.434 24 5.457z"
      fill="var(--icon-color)"
    />
  </svg>
);

export const GmailColor = ({ className }: { className?: string }) => (
  <svg role="img" xmlns="http://www.w3.org/2000/svg" viewBox="52 42 88 66" className={className}>
    <path fill="#4285f4" d="M58 108h14V74L52 59v43c0 3.32 2.69 6 6 6" />
    <path fill="#34a853" d="M120 108h14c3.32 0 6-2.69 6-6V59l-20 15" />
    <path fill="#fbbc04" d="M120 48v26l20-15v-8c0-7.42-8.47-11.65-14.4-7.2" />
    <path fill="#ea4335" d="M72 74V48l24 18 24-18v26L96 92" />
    <path fill="#c5221f" d="M52 51v8l20 15V48l-5.6-4.2c-5.94-4.45-14.4-.22-14.4 7.2" />
  </svg>
);

export const Microsoft = ({ className }: { className?: string }) => (
  <svg role="img" viewBox="0 0 448 512" xmlns="http://www.w3.org/2000/svg" className={className}>
    <title>Microsoft</title>
    <path
      d="M0 32h214.6v214.6H0V32zm233.4 0H448v214.6H233.4V32zM0 265.4h214.6V480H0V265.4zm233.4 0H448V480H233.4V265.4z"
      fill="var(--icon-color)"
    />
  </svg>
);

export const Outlook = ({ className }: { className?: string }) => (
  <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className={className}>
    <title>Outlook</title>
    <path
      d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
      fill="var(--icon-color)"
    />
  </svg>
);

export const OutlookColor = ({ className }: { className?: string }) => (
  <svg
    role="img"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 1831.085 1703.335"
    className={className}
  >
    <path
      fill="#0A2767"
      d="M1831.083,894.25c0.1-14.318-7.298-27.644-19.503-35.131h-0.213l-0.767-0.426l-634.492-375.585  c-2.74-1.851-5.583-3.543-8.517-5.067c-24.498-12.639-53.599-12.639-78.098,0c-2.934,1.525-5.777,3.216-8.517,5.067L446.486,858.693  l-0.766,0.426c-19.392,12.059-25.337,37.556-13.278,56.948c3.553,5.714,8.447,10.474,14.257,13.868l634.492,375.585  c2.749,1.835,5.592,3.527,8.517,5.068c24.498,12.639,53.599,12.639,78.098,0c2.925-1.541,5.767-3.232,8.517-5.068l634.492-375.585  C1823.49,922.545,1831.228,908.923,1831.083,894.25z"
    />
    <path
      fill="#0364B8"
      d="M520.453,643.477h416.38v381.674h-416.38V643.477z M1745.917,255.5V80.908  c1-43.652-33.552-79.862-77.203-80.908H588.204C544.552,1.046,510,37.256,511,80.908V255.5l638.75,170.333L1745.917,255.5z"
    />
    <path fill="#0078D4" d="M511,255.5h425.833v383.25H511V255.5z" />
    <path
      fill="#28A8EA"
      d="M1362.667,255.5H936.833v383.25L1362.667,1022h383.25V638.75L1362.667,255.5z"
    />
    <path fill="#0078D4" d="M936.833,638.75h425.833V1022H936.833V638.75z" />
    <path fill="#0364B8" d="M936.833,1022h425.833v383.25H936.833V1022z" />
    <path fill="#14447D" d="M520.453,1025.151h416.38v346.969h-416.38V1025.151z" />
    <path fill="#0078D4" d="M1362.667,1022h383.25v383.25h-383.25V1022z" />
    <linearGradient
      id="SVGID_1_"
      gradientUnits="userSpaceOnUse"
      x1="1128.4584"
      y1="811.0833"
      x2="1128.4584"
      y2="1.9982"
      gradientTransform="matrix(1 0 0 -1 0 1705.3334)"
    >
      <stop offset="0" style={{ stopColor: '#35B8F1' }} />
      <stop offset="1" style={{ stopColor: '#28A8EA' }} />
    </linearGradient>
    <path
      fill="url(#SVGID_1_)"
      d="M1811.58,927.593l-0.809,0.426l-634.492,356.848c-2.768,1.703-5.578,3.321-8.517,4.769  c-10.777,5.132-22.481,8.029-34.407,8.517l-34.663-20.27c-2.929-1.47-5.773-3.105-8.517-4.897L447.167,906.003h-0.298  l-21.036-11.753v722.384c0.328,48.196,39.653,87.006,87.849,86.7h1230.914c0.724,0,1.363-0.341,2.129-0.341  c10.18-0.651,20.216-2.745,29.808-6.217c4.145-1.756,8.146-3.835,11.966-6.217c2.853-1.618,7.75-5.152,7.75-5.152  c21.814-16.142,34.726-41.635,34.833-68.772V894.25C1831.068,908.067,1823.616,920.807,1811.58,927.593z"
    />
    <path
      opacity="0.5"
      fill="#0A2767"
      d="M1797.017,891.397v44.287l-663.448,456.791L446.699,906.301  c0-0.235-0.191-0.426-0.426-0.426l0,0l-63.023-37.899v-31.938l25.976-0.426l54.932,31.512l1.277,0.426l4.684,2.981  c0,0,645.563,368.346,647.267,369.197l24.698,14.478c2.129-0.852,4.258-1.703,6.813-2.555  c1.278-0.852,640.879-360.681,640.879-360.681L1797.017,891.397z"
    />
    <path
      fill="#1490DF"
      d="M1811.58,927.593l-0.809,0.468l-634.492,356.848c-2.768,1.703-5.578,3.321-8.517,4.769  c-24.641,12.038-53.457,12.038-78.098,0c-2.918-1.445-5.76-3.037-8.517-4.769L446.657,928.061l-0.766-0.468  c-12.25-6.642-19.93-19.409-20.057-33.343v722.384c0.305,48.188,39.616,87.004,87.803,86.7c0.001,0,0.002,0,0.004,0h1229.636  c48.188,0.307,87.5-38.509,87.807-86.696c0-0.001,0-0.002,0-0.004V894.25C1831.068,908.067,1823.616,920.807,1811.58,927.593z"
    />
    <path
      opacity="0.1"
      d="M1185.52,1279.629l-9.496,5.323c-2.752,1.752-5.595,3.359-8.517,4.812  c-10.462,5.135-21.838,8.146-33.47,8.857l241.405,285.479l421.107,101.476c11.539-8.716,20.717-20.178,26.7-33.343L1185.52,1279.629  z"
    />
    <path
      opacity="0.05"
      d="M1228.529,1255.442l-52.505,29.51c-2.752,1.752-5.595,3.359-8.517,4.812  c-10.462,5.135-21.838,8.146-33.47,8.857l113.101,311.838l549.538,74.989c21.649-16.254,34.394-41.743,34.407-68.815v-9.326  L1228.529,1255.442z"
    />
    <path
      fill="#28A8EA"
      d="M514.833,1703.333h1228.316c18.901,0.096,37.335-5.874,52.59-17.033l-697.089-408.331  c-2.929-1.47-5.773-3.105-8.517-4.897L447.125,906.088h-0.298l-20.993-11.838v719.914  C425.786,1663.364,465.632,1703.286,514.833,1703.333C514.832,1703.333,514.832,1703.333,514.833,1703.333z"
    />
    <path
      opacity="0.1"
      d="M1022,418.722v908.303c-0.076,31.846-19.44,60.471-48.971,72.392  c-9.148,3.931-19,5.96-28.957,5.962H425.833V383.25H511v-42.583h433.073C987.092,340.83,1021.907,375.702,1022,418.722z"
    />
    <path
      opacity="0.2"
      d="M979.417,461.305v908.302c0.107,10.287-2.074,20.469-6.388,29.808  c-11.826,29.149-40.083,48.273-71.54,48.417H425.833V383.25h475.656c12.356-0.124,24.533,2.958,35.344,8.943  C962.937,405.344,979.407,432.076,979.417,461.305z"
    />
    <path
      opacity="0.2"
      d="M979.417,461.305v823.136c-0.208,43-34.928,77.853-77.927,78.225H425.833V383.25  h475.656c12.356-0.124,24.533,2.958,35.344,8.943C962.937,405.344,979.407,432.076,979.417,461.305z"
    />
    <path
      opacity="0.2"
      d="M936.833,461.305v823.136c-0.046,43.067-34.861,78.015-77.927,78.225H425.833  V383.25h433.072c43.062,0.023,77.951,34.951,77.927,78.013C936.833,461.277,936.833,461.291,936.833,461.305z"
    />
    <linearGradient
      id="SVGID_2_"
      gradientUnits="userSpaceOnUse"
      x1="162.7469"
      y1="1383.0741"
      x2="774.0864"
      y2="324.2592"
      gradientTransform="matrix(1 0 0 -1 0 1705.3334)"
    >
      <stop offset="0" style={{ stopColor: '#1784D9' }} />
      <stop offset="0.5" style={{ stopColor: '#107AD5' }} />
      <stop offset="1" style={{ stopColor: '#0A63C9' }} />
    </linearGradient>
    <path
      fill="url(#SVGID_2_)"
      d="M78.055,383.25h780.723c43.109,0,78.055,34.947,78.055,78.055v780.723  c0,43.109-34.946,78.055-78.055,78.055H78.055c-43.109,0-78.055-34.947-78.055-78.055V461.305  C0,418.197,34.947,383.25,78.055,383.25z"
    />
    <path
      fill="#FFFFFF"
      d="M243.96,710.631c19.238-40.988,50.29-75.289,89.17-98.495c43.057-24.651,92.081-36.94,141.675-35.515  c45.965-0.997,91.321,10.655,131.114,33.683c37.414,22.312,67.547,55.004,86.742,94.109c20.904,43.09,31.322,90.512,30.405,138.396  c1.013,50.043-9.706,99.628-31.299,144.783c-19.652,40.503-50.741,74.36-89.425,97.388c-41.327,23.734-88.367,35.692-136.011,34.578  c-46.947,1.133-93.303-10.651-134.01-34.067c-37.738-22.341-68.249-55.07-87.892-94.28c-21.028-42.467-31.57-89.355-30.745-136.735  C212.808,804.859,223.158,755.686,243.96,710.631z M339.006,941.858c10.257,25.912,27.651,48.385,50.163,64.812  c22.93,16.026,50.387,24.294,78.353,23.591c29.783,1.178,59.14-7.372,83.634-24.358c22.227-16.375,39.164-38.909,48.715-64.812  c10.677-28.928,15.946-59.572,15.543-90.404c0.33-31.127-4.623-62.084-14.649-91.554c-8.855-26.607-25.246-50.069-47.182-67.537  c-23.88-17.79-53.158-26.813-82.91-25.55c-28.572-0.74-56.644,7.593-80.184,23.804c-22.893,16.496-40.617,39.168-51.1,65.365  c-23.255,60.049-23.376,126.595-0.341,186.728L339.006,941.858z"
    />
    <path fill="#50D9FF" d="M1362.667,255.5h383.25v383.25h-383.25V255.5z" />
  </svg>
);

export const Discord = ({ className }: { className?: string }) => (
  <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className={className}>
    <title>Discord</title>
    <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z" />
  </svg>
);

export const Google = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    className={className}
  >
    <path
      fill="currentColor"
      d="M11.99 13.9v-3.72h9.36c.14.63.25 1.22.25 2.05c0 5.71-3.83 9.77-9.6 9.77c-5.52 0-10-4.48-10-10S6.48 2 12 2c2.7 0 4.96.99 6.69 2.61l-2.84 2.76c-.72-.68-1.98-1.48-3.85-1.48c-3.31 0-6.01 2.75-6.01 6.12s2.7 6.12 6.01 6.12c3.83 0 5.24-2.65 5.5-4.22h-5.51z"
    ></path>
  </svg>
);

export const GitHub = ({ className }: { className?: string }) => (
  <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className={className}>
    <title>GitHub</title>
    <path d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12" />
  </svg>
);

export const Twitter = ({ className }: { className?: string }) => (
  <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className={className}>
    <title>X (Twitter)</title>
    <path d="M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z" />
  </svg>
);

export const YouTube = ({ className }: { className?: string }) => (
  <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className={className}>
    <title>YouTube</title>
    <path
      d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
      fill="var(--icon-color)"
    />
  </svg>
);

export const CurvedArrow = ({ className }: { className?: string }) => (
  <svg
    width="2em"
    height="2em"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    viewBox="0 0 16 16"
  >
    <path d="M4.26465 11.0684C4.08008 11.0684 3.88184 10.9863 3.73828 10.8428L0.744141 7.90332C0.600586 7.75977 0.518555 7.55469 0.518555 7.35645C0.518555 7.1582 0.600586 6.95312 0.744141 6.81641L3.73828 3.87695C3.88184 3.7334 4.08008 3.64453 4.26465 3.64453C4.69531 3.64453 4.97559 3.94531 4.97559 4.35547C4.97559 4.57422 4.89355 4.73145 4.75684 4.86816L3.59473 5.98926L2.74707 6.68652L3.9707 6.625H10.335C10.8408 6.625 11.0391 6.42676 11.0391 5.9209V2.89941C11.0391 2.38672 10.8408 2.18848 10.335 2.18848H7.5459C7.11523 2.18848 6.80078 1.86035 6.80078 1.45703C6.80078 1.05371 7.11523 0.725586 7.5459 0.725586H10.3828C11.8594 0.725586 12.4814 1.34766 12.4814 2.82422V5.98926C12.4814 7.44531 11.8594 8.08789 10.3828 8.08789H3.9707L2.74707 8.0332L3.59473 8.72363L4.75684 9.85156C4.89355 9.98145 4.97559 10.1455 4.97559 10.3643C4.97559 10.7744 4.69531 11.0684 4.26465 11.0684Z" />
  </svg>
);

export const Stop = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.5 7.5C4.5 5.84315 5.84315 4.5 7.5 4.5H16.5C18.1569 4.5 19.5 5.84315 19.5 7.5V16.5C19.5 18.1569 18.1569 19.5 16.5 19.5H7.5C5.84315 19.5 4.5 18.1569 4.5 16.5V7.5Z"
      fill="var(--icon-color)"
    />
  </svg>
);

export const PanelLeftOpen = ({ className }: { className?: string }) => (
  <svg
    width="14"
    height="12"
    viewBox="0 0 14 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.8 12C3.11984 12 2.27976 12 1.63803 11.673C1.07354 11.3854 0.614601 10.9265 0.32698 10.362C0 9.72024 0 8.88016 0 7.2V4.8C0 3.11984 0 2.27976 0.32698 1.63803C0.614601 1.07354 1.07354 0.614601 1.63803 0.32698C2.27976 0 3.11984 0 4.8 0H9.2C10.8802 0 11.7202 0 12.362 0.32698C12.9265 0.614601 13.3854 1.07354 13.673 1.63803C14 2.27976 14 3.11984 14 4.8V7.2C14 8.88016 14 9.72024 13.673 10.362C13.3854 10.9265 12.9265 11.3854 12.362 11.673C11.7202 12 10.8802 12 9.2 12H4.8ZM10.1 1.5C10.9401 1.5 11.3601 1.5 11.681 1.66349C11.9632 1.8073 12.1927 2.03677 12.3365 2.31901C12.5 2.63988 12.5 3.05992 12.5 3.9V8.1C12.5 8.94008 12.5 9.36012 12.3365 9.68099C12.1927 9.96323 11.9632 10.1927 11.681 10.3365C11.3601 10.5 10.9401 10.5 10.1 10.5H9.9C9.05992 10.5 8.63988 10.5 8.31901 10.3365C8.03677 10.1927 7.8073 9.96323 7.66349 9.68099C7.5 9.36012 7.5 8.94008 7.5 8.1V3.9C7.5 3.05992 7.5 2.63988 7.66349 2.31901C7.8073 2.03677 8.03677 1.8073 8.31901 1.66349C8.63988 1.5 9.05992 1.5 9.9 1.5H10.1ZM1.96094 2.82422C1.96094 2.47904 2.24076 2.19922 2.58594 2.19922H4.08594C4.43112 2.19922 4.71094 2.47904 4.71094 2.82422C4.71094 3.1694 4.43112 3.44922 4.08594 3.44922H2.58594C2.24076 3.44922 1.96094 3.1694 1.96094 2.82422ZM2.58594 4.19531C2.24076 4.19531 1.96094 4.47513 1.96094 4.82031C1.96094 5.16549 2.24076 5.44531 2.58594 5.44531H4.08594C4.43112 5.44531 4.71094 5.16549 4.71094 4.82031C4.71094 4.47513 4.43112 4.19531 4.08594 4.19531H2.58594Z"
    />
  </svg>
);

export const PencilCompose = ({ className }: { className?: string }) => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M11.4875 0.512563C10.804 -0.170854 9.696 -0.170854 9.01258 0.512563L4.75098 4.77417C4.49563 5.02951 4.29308 5.33265 4.15488 5.66628L3.30712 7.71282C3.19103 7.99307 3.25519 8.31566 3.46968 8.53017C3.68417 8.74467 4.00676 8.80885 4.28702 8.69277L6.33382 7.84501C6.66748 7.70681 6.97066 7.50423 7.22604 7.24886L11.4875 2.98744C12.1709 2.30402 12.1709 1.19598 11.4875 0.512563Z" />
    <path d="M2.75 1.5C2.05964 1.5 1.5 2.05964 1.5 2.75V9.25C1.5 9.94036 2.05964 10.5 2.75 10.5H9.25C9.94036 10.5 10.5 9.94036 10.5 9.25V7C10.5 6.58579 10.8358 6.25 11.25 6.25C11.6642 6.25 12 6.58579 12 7V9.25C12 10.7688 10.7688 12 9.25 12H2.75C1.23122 12 0 10.7688 0 9.25V2.75C0 1.23122 1.23122 4.84288e-08 2.75 4.84288e-08H5C5.41421 4.84288e-08 5.75 0.335786 5.75 0.75C5.75 1.16421 5.41421 1.5 5 1.5H2.75Z" />
  </svg>
);

export const CircleCheck = ({ className }: { className?: string }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z"
    />
  </svg>
);

export const CircleX = ({ className }: { className?: string }) => (
  <svg
    width="20"
    height="20"
    viewBox="-2 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 0.25C4.61522 0.25 0.25 4.61522 0.25 10C0.25 15.3848 4.61522 19.75 10 19.75C15.3848 19.75 19.75 15.3848 19.75 10C19.75 4.61522 15.3848 0.25 10 0.25ZM8.28033 7.21967C7.98744 6.92678 7.51256 6.92678 7.21967 7.21967C6.92678 7.51256 6.92678 7.98744 7.21967 8.28033L8.93934 10L7.21967 11.7197C6.92678 12.0126 6.92678 12.4874 7.21967 12.7803C7.51256 13.0732 7.98744 13.0732 8.28033 12.7803L10 11.0607L11.7197 12.7803C12.0126 13.0732 12.4874 13.0732 12.7803 12.7803C13.0732 12.4874 13.0732 12.0126 12.7803 11.7197L11.0607 10L12.7803 8.28033C13.0732 7.98744 13.0732 7.51256 12.7803 7.21967C12.4874 6.92678 12.0126 6.92678 11.7197 7.21967L10 8.93934L8.28033 7.21967Z"
    />
  </svg>
);

export const Inbox = ({ className }: { className?: string }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.78373 3C3.85012 3 3.01349 3.57656 2.68113 4.44901L1.1474 8.47505C1.04995 8.73086 1 9.0023 1 9.27604V10.75C1 11.9926 2.00736 13 3.25 13H12.75C13.9926 13 15 11.9926 15 10.75V9.27604C15 9.0023 14.95 8.73086 14.8526 8.47505L13.3189 4.44901C12.9865 3.57656 12.1499 3 11.2163 3H4.78373ZM4.08286 4.983C4.19365 4.69219 4.47252 4.5 4.78373 4.5H11.2163C11.5275 4.5 11.8063 4.69219 11.9171 4.983L13.4474 9H11.0352C10.7008 9 10.3886 9.1671 10.2031 9.4453L9.79687 10.0547C9.6114 10.3329 9.29917 10.5 8.96482 10.5H7.03518C6.70083 10.5 6.3886 10.3329 6.20313 10.0547L5.79687 9.4453C5.6114 9.1671 5.29917 9 4.96482 9H2.55258L4.08286 4.983Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const PaperPlane = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="mr-[8px]"
  >
    <path
      d="M3.47804 2.4043C3.2133 2.3274 2.92771 2.40193 2.73432 2.5984C2.54093 2.79487 2.47091 3.0816 2.55198 3.3451L4.98426 11.25H13.5C13.9142 11.25 14.25 11.5858 14.25 12C14.25 12.4142 13.9142 12.75 13.5 12.75H4.98427L2.55207 20.6546C2.471 20.9181 2.54102 21.2049 2.73441 21.4013C2.92781 21.5978 3.2134 21.6723 3.47814 21.5954C10.1767 19.6494 16.3974 16.5814 21.9233 12.6087C22.1193 12.4678 22.2355 12.2412 22.2355 11.9998C22.2355 11.7583 22.1193 11.5317 21.9233 11.3908C16.3974 7.41817 10.1767 4.35021 3.47804 2.4043Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Folder = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M19.5 21C21.1569 21 22.5 19.6569 22.5 18V13.5C22.5 11.8431 21.1569 10.5 19.5 10.5H4.5C2.84315 10.5 1.5 11.8431 1.5 13.5V18C1.5 19.6569 2.84315 21 4.5 21H19.5Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M1.5 10.1458V6C1.5 4.34315 2.84315 3 4.5 3H9.87868C10.4754 3 11.0477 3.23705 11.4697 3.65901L13.591 5.78033C13.7316 5.92098 13.9224 6 14.1213 6H19.5C21.1569 6 22.5 7.34315 22.5 9V10.1458C21.7039 9.43328 20.6525 9 19.5 9H4.5C3.34747 9 2.29613 9.43328 1.5 10.1458Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const FolderOpen = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M19.9057 9C20.2877 9 20.6549 9.05664 21 9.16156V9C21 7.34315 19.6569 6 18 6H14.1213C13.9224 6 13.7316 5.92098 13.591 5.78033L11.4697 3.65901C11.0477 3.23705 10.4754 3 9.87868 3H6C4.34315 3 3 4.34315 3 6V9.16152C3.34508 9.05663 3.71223 9 4.09421 9H19.9057Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M4.0943 10.5C2.72506 10.5 1.67327 11.7127 1.86691 13.0682L2.72405 19.0682C2.8824 20.1767 3.83173 21 4.95144 21H19.0486C20.1683 21 21.1176 20.1767 21.276 19.0682L22.1331 13.0682C22.3268 11.7127 21.275 10.5 19.9058 10.5H4.0943Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Bookmark = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.32022 2.57741C8.18374 2.36114 10.079 2.25 12 2.25C13.921 2.25 15.8163 2.36114 17.6798 2.57741C19.1772 2.75119 20.25 4.03722 20.25 5.50699V21C20.25 21.2599 20.1154 21.5013 19.8943 21.638C19.6732 21.7746 19.3971 21.7871 19.1646 21.6708L12 18.0885L4.83541 21.6708C4.60292 21.7871 4.32681 21.7746 4.1057 21.638C3.88459 21.5013 3.75 21.2599 3.75 21V5.50699C3.75 4.03722 4.82283 2.75119 6.32022 2.57741Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Archive = ({ className }: { className?: string }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M3 2C2.44772 2 2 2.44772 2 3V4C2 4.55228 2.44772 5 3 5H13C13.5523 5 14 4.55228 14 4V3C14 2.44772 13.5523 2 13 2H3Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3 6H13V12C13 13.1046 12.1046 14 11 14H5C3.89543 14 3 13.1046 3 12V6ZM6 8.75C6 8.33579 6.33579 8 6.75 8H9.25C9.66421 8 10 8.33579 10 8.75C10 9.16421 9.66421 9.5 9.25 9.5H6.75C6.33579 9.5 6 9.16421 6 8.75Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const ExclamationCircle = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7 13.5C10.5899 13.5 13.5 10.5899 13.5 7C13.5 3.41015 10.5899 0.5 7 0.5C3.41015 0.5 0.5 3.41015 0.5 7C0.5 10.5899 3.41015 13.5 7 13.5ZM7 3.28571C7.38463 3.28571 7.69643 3.59752 7.69643 3.98214V6.76786C7.69643 7.15248 7.38463 7.46429 7 7.46429C6.61537 7.46429 6.30357 7.15248 6.30357 6.76786V3.98214C6.30357 3.59752 6.61537 3.28571 7 3.28571ZM7 10.7143C7.51284 10.7143 7.92857 10.2986 7.92857 9.78571C7.92857 9.27288 7.51284 8.85714 7 8.85714C6.48716 8.85714 6.07143 9.27288 6.07143 9.78571C6.07143 10.2986 6.48716 10.7143 7 10.7143Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Bin = ({ className }: { className?: string }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5 3.25V4H2.75C2.33579 4 2 4.33579 2 4.75C2 5.16421 2.33579 5.5 2.75 5.5H3.05L3.86493 13.6493C3.94161 14.4161 4.58685 15 5.35748 15H10.6425C11.4131 15 12.0584 14.4161 12.1351 13.6493L12.95 5.5H13.25C13.6642 5.5 14 5.16421 14 4.75C14 4.33579 13.6642 4 13.25 4H11V3.25C11 2.00736 9.99264 1 8.75 1H7.25C6.00736 1 5 2.00736 5 3.25ZM7.25 2.5C6.83579 2.5 6.5 2.83579 6.5 3.25V4H9.5V3.25C9.5 2.83579 9.16421 2.5 8.75 2.5H7.25ZM6.05044 6.00094C6.46413 5.98025 6.81627 6.29885 6.83696 6.71255L7.11195 12.2125C7.13264 12.6262 6.81404 12.9784 6.40034 12.9991C5.98665 13.0197 5.63451 12.7011 5.61383 12.2875L5.33883 6.78745C5.31814 6.37376 5.63674 6.02162 6.05044 6.00094ZM9.95034 6.00094C10.364 6.02162 10.6826 6.37376 10.662 6.78745L10.387 12.2875C10.3663 12.7011 10.0141 13.0197 9.60044 12.9991C9.18674 12.9784 8.86814 12.6262 8.88883 12.2125L9.16383 6.71255C9.18451 6.29885 9.53665 5.98025 9.95034 6.00094Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const SettingsGear = ({ className }: { className?: string }) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.45494 0.450248C5.4805 0.194648 5.69558 0 5.95246 0H8.04747C8.30435 0 8.51943 0.194647 8.54499 0.450248L8.73073 2.30766C9.26926 2.50637 9.76418 2.79513 10.1972 3.15568L11.9 2.38721C12.1342 2.28155 12.4103 2.37049 12.5387 2.59295L13.5862 4.40728C13.7147 4.62975 13.6536 4.91334 13.4451 5.06327L11.9286 6.15339C11.9755 6.42858 12 6.71143 12 7C12 7.28864 11.9755 7.57157 11.9286 7.84682L13.4451 8.93696C13.6536 9.0869 13.7147 9.37049 13.5862 9.59295L12.5387 11.4073C12.4103 11.6297 12.1342 11.7187 11.9 11.613L10.197 10.8445C9.76404 11.205 9.26918 11.4937 8.73073 11.6923L8.54499 13.5498C8.51943 13.8054 8.30435 14 8.04747 14H5.95246C5.69558 14 5.4805 13.8054 5.45494 13.5498L5.2692 11.6923C4.73067 11.4936 4.23575 11.2049 3.80271 10.8443L2.0999 11.6128C1.86577 11.7185 1.58966 11.6295 1.46122 11.4071L0.413712 9.59272C0.285275 9.37026 0.346303 9.08667 0.554879 8.93673L2.07134 7.84661C2.02441 7.57142 1.99996 7.28857 1.99996 7C1.99996 6.71136 2.02442 6.42843 2.07138 6.15318L0.554879 5.06304C0.346302 4.9131 0.285274 4.62951 0.413712 4.40705L1.46122 2.59272C1.58966 2.37025 1.86577 2.28131 2.0999 2.38698L3.80289 3.15552C4.23589 2.79505 4.73075 2.50634 5.2692 2.30766L5.45494 0.450248ZM5.27747 8.01699L5.25611 7.98C5.09301 7.69039 4.99996 7.35606 4.99996 7C4.99996 5.89543 5.8954 5 6.99996 5C7.73323 5 8.37433 5.39461 8.72249 5.98306L8.74379 6.01995C8.90691 6.30957 8.99996 6.64392 8.99996 7C8.99996 8.10457 8.10453 9 6.99996 9C6.26672 9 5.62563 8.60541 5.27747 8.01699Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const LockIcon = ({ className }: { className?: string }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.5 5C5.5 3.34315 6.84315 2 8.5 2C10.1569 2 11.5 3.34315 11.5 5V6.5H5.5V5ZM4 6.5V5C4 2.51472 6.01472 0.5 8.5 0.5C10.9853 0.5 13 2.51472 13 5V6.5H13.25C14.2165 6.5 15 7.2835 15 8.25V13.25C15 14.2165 14.2165 15 13.25 15H3.75C2.7835 15 2 14.2165 2 13.25V8.25C2 7.2835 2.7835 6.5 3.75 6.5H4Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const MessageSquare = ({ className }: { className?: string }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1 8.74053C1 9.72321 1.71341 10.5653 2.68906 10.6827C3.5937 10.7915 4.50678 10.8729 5.42746 10.926C5.78969 10.9469 6.11506 11.1572 6.27733 11.4817L7.32918 13.5854C7.45622 13.8395 7.71592 14 8 14C8.28408 14 8.54378 13.8395 8.67082 13.5854L9.72265 11.4817C9.88492 11.1572 10.2103 10.9469 10.5725 10.9261C11.4932 10.873 12.4063 10.7916 13.3109 10.6828C14.2866 10.5654 15 9.72332 15 8.74062V4.25938C15 3.27668 14.2866 2.43458 13.3109 2.3172C11.57 2.10777 9.79777 2 8.00039 2C6.20273 2 4.43025 2.10781 2.68906 2.3173C1.71341 2.43469 1 3.27679 1 4.25947V8.74053ZM4 5.25C4 4.83579 4.33579 4.5 4.75 4.5H11.25C11.6642 4.5 12 4.83579 12 5.25C12 5.66421 11.6642 6 11.25 6H4.75C4.33579 6 4 5.66421 4 5.25ZM4.75 7C4.33579 7 4 7.33579 4 7.75C4 8.16421 4.33579 8.5 4.75 8.5H7.25C7.66421 8.5 8 8.16421 8 7.75C8 7.33579 7.66421 7 7.25 7H4.75Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Users = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="20"
    viewBox="0 0 22 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M3.5 4.375C3.5 2.09683 5.34683 0.25 7.625 0.25C9.90317 0.25 11.75 2.09683 11.75 4.375C11.75 6.65317 9.90317 8.5 7.625 8.5C5.34683 8.5 3.5 6.65317 3.5 4.375Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M13.25 6.625C13.25 4.76104 14.761 3.25 16.625 3.25C18.489 3.25 20 4.76104 20 6.625C20 8.48896 18.489 10 16.625 10C14.761 10 13.25 8.48896 13.25 6.625Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M0.5 17.125C0.5 13.19 3.68997 10 7.625 10C11.56 10 14.75 13.19 14.75 17.125V17.1276C14.75 17.1674 14.7496 17.2074 14.749 17.2469C14.7446 17.5054 14.6074 17.7435 14.3859 17.8768C12.4107 19.0661 10.0966 19.75 7.625 19.75C5.15343 19.75 2.8393 19.0661 0.864061 17.8768C0.642563 17.7435 0.505373 17.5054 0.501026 17.2469C0.500345 17.2064 0.5 17.1657 0.5 17.125Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M16.2498 17.1281C16.2498 17.1762 16.2494 17.2244 16.2486 17.2722C16.2429 17.6108 16.1612 17.9378 16.0157 18.232C16.2172 18.2439 16.4203 18.25 16.6248 18.25C18.2206 18.25 19.732 17.8803 21.0764 17.2213C21.3234 17.1002 21.4843 16.8536 21.4957 16.5787C21.4984 16.5111 21.4998 16.4432 21.4998 16.375C21.4998 13.6826 19.3172 11.5 16.6248 11.5C15.8784 11.5 15.1711 11.6678 14.5387 11.9676C15.6135 13.4061 16.2498 15.1912 16.2498 17.125V17.1281Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const ArrowLeft = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.0303 3.96967C11.3232 4.26256 11.3232 4.73744 11.0303 5.03033L4.81066 11.25H21C21.4142 11.25 21.75 11.5858 21.75 12C21.75 12.4142 21.4142 12.75 21 12.75H4.81066L11.0303 18.9697C11.3232 19.2626 11.3232 19.7374 11.0303 20.0303C10.7374 20.3232 10.2626 20.3232 9.96967 20.0303L2.46967 12.5303C2.17678 12.2374 2.17678 11.7626 2.46967 11.4697L9.96967 3.96967C10.2626 3.67678 10.7374 3.67678 11.0303 3.96967Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Sparkles = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8 3.5C8.33486 3.5 8.62915 3.72198 8.72114 4.04396L9.53434 6.89015C9.89028 8.13593 10.8641 9.10972 12.1099 9.46566L14.956 10.2789C15.278 10.3709 15.5 10.6651 15.5 11C15.5 11.3349 15.278 11.6291 14.956 11.7211L12.1098 12.5343C10.8641 12.8903 9.89028 13.8641 9.53434 15.1099L8.72114 17.956C8.62915 18.278 8.33486 18.5 8 18.5C7.66514 18.5 7.37085 18.278 7.27886 17.956L6.46566 15.1099C6.10972 13.8641 5.13593 12.8903 3.89015 12.5343L1.04396 11.7211C0.721983 11.6291 0.5 11.3349 0.5 11C0.5 10.6651 0.721983 10.3709 1.04396 10.2789L3.89015 9.46566C5.13593 9.10972 6.10972 8.13593 6.46566 6.89015L7.27886 4.04396C7.37085 3.72198 7.66514 3.5 8 3.5Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17 0.5C17.3442 0.5 17.6441 0.734223 17.7276 1.0681L17.9865 2.10356C18.2216 3.04406 18.9559 3.7784 19.8964 4.01353L20.9319 4.27239C21.2658 4.35586 21.5 4.65585 21.5 5C21.5 5.34415 21.2658 5.64414 20.9319 5.72761L19.8964 5.98647C18.9559 6.2216 18.2216 6.95594 17.9865 7.89644L17.7276 8.9319C17.6441 9.26578 17.3442 9.5 17 9.5C16.6558 9.5 16.3559 9.26578 16.2724 8.9319L16.0135 7.89644C15.7784 6.95594 15.0441 6.2216 14.1036 5.98647L13.0681 5.72761C12.7342 5.64414 12.5 5.34415 12.5 5C12.5 4.65585 12.7342 4.35586 13.0681 4.27239L14.1036 4.01353C15.0441 3.7784 15.7784 3.04406 16.0135 2.10356L16.2724 1.0681C16.3559 0.734223 16.6558 0.5 17 0.5Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.5 14C15.8228 14 16.1094 14.2066 16.2115 14.5128L16.6058 15.6956C16.7551 16.1435 17.1065 16.4949 17.5544 16.6442L18.7372 17.0385C19.0434 17.1406 19.25 17.4272 19.25 17.75C19.25 18.0728 19.0434 18.3594 18.7372 18.4615L17.5544 18.8558C17.1065 19.0051 16.7551 19.3565 16.6058 19.8044L16.2115 20.9872C16.1094 21.2934 15.8228 21.5 15.5 21.5C15.1772 21.5 14.8906 21.2934 14.7885 20.9872L14.3942 19.8044C14.2449 19.3565 13.8935 19.0051 13.4456 18.8558L12.2628 18.4615C11.9566 18.3594 11.75 18.0728 11.75 17.75C11.75 17.4272 11.9566 17.1406 12.2628 17.0385L13.4456 16.6442C13.8935 16.4949 14.2449 16.1435 14.3942 15.6956L14.7885 14.5128C14.8906 14.2066 15.1772 14 15.5 14Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Tabs = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="18"
    viewBox="0 0 22 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.5 2.625C0.5 1.58947 1.33947 0.75 2.375 0.75H19.625C20.6605 0.75 21.5 1.58947 21.5 2.625V15.375C21.5 16.4105 20.6605 17.25 19.625 17.25H2.375C1.33947 17.25 0.5 16.4105 0.5 15.375V2.625ZM20 6.375C20 6.16789 19.8321 6 19.625 6H12.125C11.9179 6 11.75 6.16789 11.75 6.375V7.875C11.75 8.08211 11.9179 8.25 12.125 8.25H19.625C19.8321 8.25 20 8.08211 20 7.875V6.375ZM20 10.125C20 9.91789 19.8321 9.75 19.625 9.75H12.125C11.9179 9.75 11.75 9.91789 11.75 10.125V11.625C11.75 11.8321 11.9179 12 12.125 12H19.625C19.8321 12 20 11.8321 20 11.625V10.125ZM20 13.875C20 13.6679 19.8321 13.5 19.625 13.5H12.125C11.9179 13.5 11.75 13.6679 11.75 13.875V15.375C11.75 15.5821 11.9179 15.75 12.125 15.75H19.625C19.8321 15.75 20 15.5821 20 15.375V13.875ZM9.875 15.75C10.0821 15.75 10.25 15.5821 10.25 15.375V13.875C10.25 13.6679 10.0821 13.5 9.875 13.5H2.375C2.16789 13.5 2 13.6679 2 13.875V15.375C2 15.5821 2.16789 15.75 2.375 15.75H9.875ZM2.375 12H9.875C10.0821 12 10.25 11.8321 10.25 11.625V10.125C10.25 9.91789 10.0821 9.75 9.875 9.75H2.375C2.16789 9.75 2 9.91789 2 10.125V11.625C2 11.8321 2.16789 12 2.375 12ZM2.375 8.25H9.875C10.0821 8.25 10.25 8.08211 10.25 7.875V6.375C10.25 6.16789 10.0821 6 9.875 6H2.375C2.16789 6 2 6.16789 2 6.375V7.875C2 8.08211 2.16789 8.25 2.375 8.25Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const ArrowCircle = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.16137 9.63167C4.46929 4.75046 9.48657 1.85373 14.3678 3.16164C15.9763 3.59264 17.3715 4.42788 18.4708 5.52908C18.4709 5.52921 18.4711 5.52935 18.4712 5.52948L20.1153 7.17361V4.35558C20.1153 3.85852 20.5183 3.45558 21.0153 3.45558C21.5124 3.45558 21.9153 3.85852 21.9153 4.35558V9.3464V9.34818C21.9153 9.84524 21.5124 10.2482 21.0153 10.2482H16.0227C15.5257 10.2482 15.1227 9.84524 15.1227 9.34818C15.1227 8.85112 15.5257 8.44818 16.0227 8.44818H18.8443L17.1978 6.80167L17.1972 6.80108C16.3155 5.9177 15.1967 5.24726 13.9019 4.90031C9.98093 3.84969 5.95066 6.17657 4.90003 10.0975C4.77139 10.5777 4.27788 10.8626 3.79776 10.7339C3.31764 10.6053 3.03272 10.1118 3.16137 9.63167ZM20.2014 13.2658C20.6815 13.3944 20.9665 13.8879 20.8378 14.3681C19.5299 19.2493 14.5126 22.146 9.63139 20.8381C8.0229 20.4071 6.62767 19.5719 5.5284 18.4707C5.52827 18.4705 5.52814 18.4704 5.52801 18.4703L3.88411 16.8253V19.6442C3.88411 20.1412 3.48116 20.5442 2.98411 20.5442C2.48705 20.5442 2.08411 20.1412 2.08411 19.6442V14.6515C2.08411 14.1545 2.48705 13.7515 2.98411 13.7515L7.97675 13.7515C8.4738 13.7515 8.87675 14.1545 8.87675 14.6515C8.87675 15.1486 8.4738 15.5515 7.97675 15.5515L5.1559 15.5515L6.8016 17.1983L6.80199 17.1987C7.68371 18.082 8.80245 18.7525 10.0973 19.0994C14.0182 20.15 18.0485 17.8232 19.0991 13.9022C19.2278 13.4221 19.7213 13.1371 20.2014 13.2658Z"
    />
  </svg>
);

export const ExclamationTriangle = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.40123 3.0034C10.5557 1.00229 13.4439 1.00229 14.5983 3.0034L21.9527 15.7509C23.1065 17.7509 21.6631 20.2501 19.3541 20.2501H4.64546C2.33649 20.2501 0.893061 17.7509 2.04691 15.7509L9.40123 3.0034ZM12 8.25C12.4142 8.25 12.75 8.58579 12.75 9V12.75C12.75 13.1642 12.4142 13.5 12 13.5C11.5858 13.5 11.25 13.1642 11.25 12.75V9C11.25 8.58579 11.5858 8.25 12 8.25ZM12 16.5C12.4142 16.5 12.75 16.1642 12.75 15.75C12.75 15.3358 12.4142 15 12 15C11.5858 15 11.25 15.3358 11.25 15.75C11.25 16.1642 11.5858 16.5 12 16.5Z"
    />
  </svg>
);

export const Bell = ({ className }: { className?: string }) => (
  <svg
    width="12"
    height="14"
    viewBox="0 0 12 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.5 4.375C9.5 2.442 7.933 0.875 6 0.875C4.067 0.875 2.5 2.442 2.5 4.375V6.45634C2.5 6.80444 2.36172 7.13828 2.11558 7.38442L1.00628 8.49372C0.842187 8.65781 0.75 8.88037 0.75 9.11244V9.625C0.75 10.1082 1.14175 10.5 1.625 10.5H3.375C3.375 11.9497 4.55025 13.125 6 13.125C7.44975 13.125 8.625 11.9497 8.625 10.5H10.375C10.8582 10.5 11.25 10.1082 11.25 9.625V9.11244C11.25 8.88037 11.1578 8.65781 10.9937 8.49372L9.88442 7.38442C9.63828 7.13828 9.5 6.80444 9.5 6.45634V4.375ZM4.6875 10.5C4.6875 11.2249 5.27513 11.8125 6 11.8125C6.72487 11.8125 7.3125 11.2249 7.3125 10.5H4.6875Z"
    />
  </svg>
);

export const Tag = ({ className }: { className?: string }) => (
  <svg
    width="15.5"
    height="15.5"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      className={className}
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.9375 1.75C2.72938 1.75 1.75 2.72938 1.75 3.9375V6.45634C1.75 7.03651 1.98047 7.5929 2.3907 8.00314L6.3282 11.9406C7.18248 12.7949 8.56752 12.7949 9.4218 11.9406L11.9406 9.4218C12.7949 8.56752 12.7949 7.18248 11.9406 6.3282L8.00314 2.3907C7.59291 1.98047 7.03651 1.75 6.45634 1.75H3.9375ZM4.375 5.25C4.85825 5.25 5.25 4.85825 5.25 4.375C5.25 3.89175 4.85825 3.5 4.375 3.5C3.89175 3.5 3.5 3.89175 3.5 4.375C3.5 4.85825 3.89175 5.25 4.375 5.25Z"
    />
  </svg>
);

export const User = ({ className }: { className?: string }) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M7.00012 7C8.44987 7 9.62512 5.82475 9.62512 4.375C9.62512 2.92525 8.44987 1.75 7.00012 1.75C5.55037 1.75 4.37512 2.92525 4.37512 4.375C4.37512 5.82475 5.55037 7 7.00012 7Z" />
    <path d="M11.143 12.25C11.6841 12.25 12.0992 11.7591 11.9063 11.2536C11.1524 9.27818 9.23987 7.875 6.99962 7.875C4.75936 7.875 2.84683 9.27818 2.09296 11.2536C1.90004 11.7591 2.31515 12.25 2.85623 12.25H11.143Z" />
  </svg>
);

export const Ticket = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="16"
    viewBox="0 0 22 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.5 2.375C0.5 1.33947 1.33947 0.5 2.375 0.5H19.625C20.6605 0.5 21.5 1.33947 21.5 2.375V5.40135C21.5 5.66907 21.3573 5.91649 21.1255 6.05055C20.4511 6.44072 20 7.16813 20 8C20 8.83187 20.4511 9.55928 21.1255 9.94945C21.3573 10.0835 21.5 10.3309 21.5 10.5987V13.625C21.5 14.6605 20.6605 15.5 19.625 15.5H2.375C1.33947 15.5 0.5 14.6605 0.5 13.625V10.5987C0.5 10.3309 0.642712 10.0835 0.874456 9.94945C1.54894 9.55928 2 8.83187 2 8C2 7.16813 1.54894 6.44072 0.874456 6.05055C0.642712 5.91649 0.5 5.66907 0.5 5.40135V2.375ZM15.5 1.25C15.9142 1.25 16.25 1.58579 16.25 2V2.75C16.25 3.16421 15.9142 3.5 15.5 3.5C15.0858 3.5 14.75 3.16421 14.75 2.75V2C14.75 1.58579 15.0858 1.25 15.5 1.25ZM16.25 5.75C16.25 5.33579 15.9142 5 15.5 5C15.0858 5 14.75 5.33579 14.75 5.75V6.5C14.75 6.91421 15.0858 7.25 15.5 7.25C15.9142 7.25 16.25 6.91421 16.25 6.5V5.75ZM15.5 8.75C15.9142 8.75 16.25 9.08579 16.25 9.5V10.25C16.25 10.6642 15.9142 11 15.5 11C15.0858 11 14.75 10.6642 14.75 10.25V9.5C14.75 9.08579 15.0858 8.75 15.5 8.75ZM16.25 13.25C16.25 12.8358 15.9142 12.5 15.5 12.5C15.0858 12.5 14.75 12.8358 14.75 13.25V14C14.75 14.4142 15.0858 14.75 15.5 14.75C15.9142 14.75 16.25 14.4142 16.25 14V13.25ZM5 8C5 7.58579 5.33579 7.25 5.75 7.25H11C11.4142 7.25 11.75 7.58579 11.75 8C11.75 8.41421 11.4142 8.75 11 8.75H5.75C5.33579 8.75 5 8.41421 5 8ZM5.75 10.25C5.33579 10.25 5 10.5858 5 11C5 11.4142 5.33579 11.75 5.75 11.75H8.75C9.16421 11.75 9.5 11.4142 9.5 11C9.5 10.5858 9.16421 10.25 8.75 10.25H5.75Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const GroupPeople = ({ className }: { className?: string }) => (
  <svg
    width="18"
    height="14"
    viewBox="0 0 18 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M9.00146 7C10.7274 7 12.1265 5.60089 12.1265 3.875C12.1265 2.14911 10.7274 0.75 9.00146 0.75C7.27557 0.75 5.87646 2.14911 5.87646 3.875C5.87646 5.60089 7.27557 7 9.00146 7Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M2.94656 11.7043C3.14682 10.9176 3.49617 10.1905 3.96282 9.55482C3.77306 9.51883 3.57723 9.5 3.377 9.5C2.13392 9.5 1.06035 10.2258 0.556993 11.2768C0.386308 11.6332 0.683993 12 1.07913 12H2.88972C2.90214 11.9035 2.92094 11.8049 2.94656 11.7043Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M15.0607 11.7043C15.0863 11.8049 15.1051 11.9035 15.1176 12H16.9249C17.32 12 17.6177 11.6332 17.447 11.2768C16.9437 10.2258 15.8701 9.5 14.627 9.5C14.4278 9.5 14.2329 9.51864 14.0441 9.55428C14.5109 10.1901 14.8604 10.9174 15.0607 11.7043Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M16.5015 6.375C16.5015 7.41053 15.662 8.25 14.6265 8.25C13.5909 8.25 12.7515 7.41053 12.7515 6.375C12.7515 5.33947 13.5909 4.5 14.6265 4.5C15.662 4.5 16.5015 5.33947 16.5015 6.375Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M3.37646 8.25C4.412 8.25 5.25146 7.41053 5.25146 6.375C5.25146 5.33947 4.412 4.5 3.37646 4.5C2.34093 4.5 1.50146 5.33947 1.50146 6.375C1.50146 7.41053 2.34093 8.25 3.37646 8.25Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M5.25187 13.25C4.56152 13.25 3.98584 12.6817 4.15615 12.0126C4.7068 9.8497 6.66752 8.25 9.00187 8.25C11.3362 8.25 13.2969 9.8497 13.8476 12.0126C14.0179 12.6817 13.4422 13.25 12.7519 13.25H5.25187Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Filter = ({ className }: { className?: string }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M6.5 2.25C6.5 1.83579 6.16421 1.5 5.75 1.5C5.33579 1.5 5 1.83579 5 2.25V5.25C5 5.66421 5.33579 6 5.75 6C6.16421 6 6.5 5.66421 6.5 5.25V4.5H13.25C13.6642 4.5 14 4.16421 14 3.75C14 3.33579 13.6642 3 13.25 3H6.5V2.25Z" />
    <path d="M11 6.5C11 6.08579 10.6642 5.75 10.25 5.75C9.83579 5.75 9.5 6.08579 9.5 6.5V9.5C9.5 9.91421 9.83579 10.25 10.25 10.25C10.6642 10.25 11 9.91421 11 9.5V8.75H13.25C13.6642 8.75 14 8.41421 14 8C14 7.58579 13.6642 7.25 13.25 7.25H11V6.5Z" />
    <path d="M5.75 10C6.16421 10 6.5 10.3358 6.5 10.75V11.5H13.25C13.6642 11.5 14 11.8358 14 12.25C14 12.6642 13.6642 13 13.25 13H6.5V13.75C6.5 14.1642 6.16421 14.5 5.75 14.5C5.33579 14.5 5 14.1642 5 13.75V10.75C5 10.3358 5.33579 10 5.75 10Z" />
    <path d="M2.75 7.25H8.5V8.75H2.75C2.33579 8.75 2 8.41421 2 8C2 7.58579 2.33579 7.25 2.75 7.25Z" />
    <path d="M4 3H2.75C2.33579 3 2 3.33579 2 3.75C2 4.16421 2.33579 4.5 2.75 4.5H4V3Z" />
    <path d="M2.75 11.5H4V13H2.75C2.33579 13 2 12.6642 2 12.25C2 11.8358 2.33579 11.5 2.75 11.5Z" />
  </svg>
);

export const Lightning = ({ className }: { className?: string }) => (
  <svg
    width="12"
    height="14"
    viewBox="0 0 12 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.58011 0.0765862C7.88578 0.226414 8.05221 0.563318 7.98545 0.897124L7.16486 5.00004H11.25C11.5437 5.00004 11.8103 5.1714 11.9323 5.43851C12.0542 5.70562 12.0091 6.01933 11.8168 6.24123L5.31678 13.7412C5.09383 13.9985 4.72559 14.0733 4.41991 13.9235C4.11424 13.7737 3.94781 13.4368 4.01458 13.103L4.83516 9.00004H0.750011C0.456372 9.00004 0.189743 8.82868 0.0677731 8.56157C-0.0541966 8.29446 -0.00906855 7.98074 0.183245 7.75884L6.68324 0.258839C6.90619 0.00158963 7.27444 -0.0732414 7.58011 0.0765862Z"
    />
  </svg>
);

export const Mail = ({ className }: { className?: string }) => (
  <svg
    width="14"
    height="16"
    viewBox="0 0 22 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M0.5 5.6691V14.25C0.5 15.9069 1.84315 17.25 3.5 17.25H18.5C20.1569 17.25 21.5 15.9069 21.5 14.25V5.6691L12.5723 11.1631C11.6081 11.7564 10.3919 11.7564 9.42771 11.1631L0.5 5.6691Z" />
    <path d="M21.5 3.90783V3.75C21.5 2.09315 20.1569 0.75 18.5 0.75H3.5C1.84315 0.75 0.5 2.09315 0.5 3.75V3.90783L10.2139 9.88558C10.696 10.1823 11.304 10.1823 11.7861 9.88558L21.5 3.90783Z" />
  </svg>
);

export const X = ({ className }: { className?: string }) => (
  <svg
    width="2"
    height="2"
    viewBox="1.5 2 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M6.28033 5.21967C5.98744 4.92678 5.51256 4.92678 5.21967 5.21967C4.92678 5.51256 4.92678 5.98744 5.21967 6.28033L8.93934 10L5.21967 13.7197C4.92678 14.0126 4.92678 14.4874 5.21967 14.7803C5.51256 15.0732 5.98744 15.0732 6.28033 14.7803L10 11.0607L13.7197 14.7803C14.0126 15.0732 14.4874 15.0732 14.7803 14.7803C15.0732 14.4874 15.0732 14.0126 14.7803 13.7197L11.0607 10L14.7803 6.28033C15.0732 5.98744 15.0732 5.51256 14.7803 5.21967C14.4874 4.92678 14.0126 4.92678 13.7197 5.21967L10 8.93934L6.28033 5.21967Z" />
  </svg>
);

export const ChevronLeft = ({ className }: { className?: string }) => (
  <svg
    width="5"
    height="8"
    viewBox="-2.5 -2 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.78033 0.21967C5.07322 0.512563 5.07322 0.987437 4.78033 1.28033L2.06066 4L4.78033 6.71967C5.07322 7.01256 5.07322 7.48744 4.78033 7.78033C4.48744 8.07322 4.01256 8.07322 3.71967 7.78033L0.46967 4.53033C0.176777 4.23744 0.176777 3.76256 0.46967 3.46967L3.71967 0.21967C4.01256 -0.0732233 4.48744 -0.0732233 4.78033 0.21967Z"
    />
  </svg>
);

export const ChevronRight = ({ className }: { className?: string }) => (
  <svg
    width="5"
    height="8"
    viewBox="-4 -2 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.21967 0.21967C0.512563 -0.0732233 0.987437 -0.0732233 1.28033 0.21967L4.53033 3.46967C4.82322 3.76256 4.82322 4.23744 4.53033 4.53033L1.28033 7.78033C0.987437 8.07322 0.512563 8.07322 0.21967 7.78033C-0.0732233 7.48744 -0.0732233 7.01256 0.21967 6.71967L2.93934 4L0.21967 1.28033C-0.0732232 0.987437 -0.0732232 0.512563 0.21967 0.21967Z"
    />
  </svg>
);

export const Reply = ({ className }: { className?: string }) => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.5 7.75C10.5 6.23122 9.26878 5 7.75 5H2.56066L4.78033 7.21967C5.07322 7.51256 5.07322 7.98744 4.78033 8.28033C4.48744 8.57322 4.01256 8.57322 3.71967 8.28033L0.21967 4.78033C-0.0732234 4.48744 -0.0732233 4.01256 0.21967 3.71967L3.71967 0.21967C4.01256 -0.0732233 4.48744 -0.0732233 4.78033 0.21967C5.07322 0.512563 5.07322 0.987437 4.78033 1.28033L2.56066 3.5L7.75 3.5C10.0972 3.5 12 5.40279 12 7.75C12 10.0972 10.0972 12 7.75 12H6.75C6.33579 12 6 11.6642 6 11.25C6 10.8358 6.33579 10.5 6.75 10.5H7.75C9.26878 10.5 10.5 9.26878 10.5 7.75Z"
    />
  </svg>
);

export const ReplyAll = ({ className }: { className?: string }) => (
  <svg
    width="17"
    height="17"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14 9.75C14 8.23122 12.7688 7 11.25 7H7.56066L9.78033 9.21967C10.0732 9.51256 10.0732 9.98744 9.78033 10.2803C9.48744 10.5732 9.01256 10.5732 8.71967 10.2803L5.21967 6.78033C4.92678 6.48744 4.92678 6.01256 5.21967 5.71967L8.71967 2.21967C9.01256 1.92678 9.48744 1.92678 9.78033 2.21967C10.0732 2.51256 10.0732 2.98744 9.78033 3.28033L7.56066 5.5H11.25C13.5972 5.5 15.5 7.40279 15.5 9.75C15.5 12.0972 13.5972 14 11.25 14H10.25C9.83579 14 9.5 13.6642 9.5 13.25C9.5 12.8358 9.83579 12.5 10.25 12.5H11.25C12.7688 12.5 14 11.2688 14 9.75Z"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.21967 6.78033C0.926777 6.48744 0.926777 6.01256 1.21967 5.71967L4.71967 2.21967C5.01256 1.92678 5.48744 1.92678 5.78033 2.21967C6.07322 2.51256 6.07322 2.98744 5.78033 3.28033L3.06066 6L5.78033 8.71967C6.07322 9.01256 6.07322 9.48744 5.78033 9.78033C5.48744 10.0732 5.01256 10.0732 4.71967 9.78033L1.21967 6.78033Z"
    />
  </svg>
);

export const Forward = ({ className }: { className?: string }) => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 6C-1.81059e-08 5.58579 0.335786 5.25 0.75 5.25L9.43934 5.25L6.21967 2.03033C5.92678 1.73744 5.92678 1.26256 6.21967 0.96967C6.51256 0.676777 6.98744 0.676777 7.28033 0.96967L11.7803 5.46967C12.0732 5.76256 12.0732 6.23744 11.7803 6.53033L7.28033 11.0303C6.98744 11.3232 6.51256 11.3232 6.21967 11.0303C5.92678 10.7374 5.92678 10.2626 6.21967 9.96967L9.43934 6.75L0.75 6.75C0.335786 6.75 1.81059e-08 6.41421 0 6Z"
    />
  </svg>
);

export const Star = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.7881 3.21068C11.2365 2.13274 12.7635 2.13273 13.2119 3.21068L15.2938 8.2164L20.6979 8.64964C21.8617 8.74293 22.3336 10.1952 21.4469 10.9547L17.3296 14.4817L18.5875 19.7551C18.8584 20.8908 17.623 21.7883 16.6267 21.1798L12 18.3538L7.37334 21.1798C6.37703 21.7883 5.14163 20.8908 5.41252 19.7551L6.67043 14.4817L2.55309 10.9547C1.66645 10.1952 2.13832 8.74293 3.30206 8.64964L8.70615 8.2164L10.7881 3.21068Z"
    />
  </svg>
);

export const ThreeDots = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="12"
    height="4"
    viewBox="0 0 12 4"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M0 2C0 1.17157 0.671573 0.5 1.5 0.5C2.32843 0.5 3 1.17157 3 2C3 2.82843 2.32843 3.5 1.5 3.5C0.671573 3.5 0 2.82843 0 2Z" />
    <path d="M4.5 2C4.5 1.17157 5.17157 0.5 6 0.5C6.82843 0.5 7.5 1.17157 7.5 2C7.5 2.82843 6.82843 3.5 6 3.5C5.17157 3.5 4.5 2.82843 4.5 2Z" />
    <path d="M10.5 0.5C9.67157 0.5 9 1.17157 9 2C9 2.82843 9.67157 3.5 10.5 3.5C11.3284 3.5 12 2.82843 12 2C12 1.17157 11.3284 0.5 10.5 0.5Z" />
  </svg>
);

export const Trash = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5 3.25V4H2.75C2.33579 4 2 4.33579 2 4.75C2 5.16421 2.33579 5.5 2.75 5.5H3.05L3.86493 13.6493C3.94161 14.4161 4.58685 15 5.35748 15H10.6425C11.4131 15 12.0584 14.4161 12.1351 13.6493L12.95 5.5H13.25C13.6642 5.5 14 5.16421 14 4.75C14 4.33579 13.6642 4 13.25 4H11V3.25C11 2.00736 9.99264 1 8.75 1H7.25C6.00736 1 5 2.00736 5 3.25ZM7.25 2.5C6.83579 2.5 6.5 2.83579 6.5 3.25V4H9.5V3.25C9.5 2.83579 9.16421 2.5 8.75 2.5H7.25ZM6.05044 6.00094C6.46413 5.98025 6.81627 6.29885 6.83696 6.71255L7.11195 12.2125C7.13264 12.6262 6.81404 12.9784 6.40034 12.9991C5.98665 13.0197 5.63451 12.7011 5.61383 12.2875L5.33883 6.78745C5.31814 6.37376 5.63674 6.02162 6.05044 6.00094ZM9.95034 6.00094C10.364 6.02162 10.6826 6.37376 10.662 6.78745L10.387 12.2875C10.3663 12.7011 10.0141 13.0197 9.60044 12.9991C9.18674 12.9784 8.86814 12.6262 8.88883 12.2125L9.16383 6.71255C9.18451 6.29885 9.53665 5.98025 9.95034 6.00094Z"
    />
  </svg>
);

export const Expand = ({ className }: { className?: string }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M12.85 1.75022C12.85 1.25316 13.2529 0.85022 13.75 0.85022L18.25 0.85022C18.747 0.85022 19.15 1.25316 19.15 1.75022V6.25022C19.15 6.74728 18.747 7.15022 18.25 7.15022C17.7529 7.15022 17.35 6.74728 17.35 6.25022V3.92301L13.6364 7.63662C13.2849 7.98809 12.715 7.98809 12.3636 7.63662C12.0121 7.28514 12.0121 6.7153 12.3636 6.36382L16.0772 2.65022L13.75 2.65022C13.2529 2.65022 12.85 2.24728 12.85 1.75022Z" />
    <path d="M0.849976 1.75022C0.849976 1.25316 1.25292 0.85022 1.74998 0.85022H6.24998C6.74703 0.85022 7.14998 1.25316 7.14998 1.75022C7.14998 2.24728 6.74703 2.65022 6.24998 2.65022H3.92277L7.63637 6.36382C7.98784 6.7153 7.98784 7.28514 7.63637 7.63662C7.2849 7.98809 6.71505 7.98809 6.36358 7.63662L2.64998 3.92301V6.25022C2.64998 6.74728 2.24703 7.15022 1.74998 7.15022C1.25292 7.15022 0.849976 6.74728 0.849976 6.25022V1.75022Z" />
    <path d="M7.63637 12.3638C7.98784 12.7153 7.98784 13.2851 7.63637 13.6366L3.92277 17.3502H6.24998C6.74703 17.3502 7.14998 17.7532 7.14998 18.2502C7.14998 18.7473 6.74703 19.1502 6.24998 19.1502H1.74998C1.25292 19.1502 0.849976 18.7473 0.849976 18.2502V13.7502C0.849976 13.2532 1.25292 12.8502 1.74998 12.8502C2.24703 12.8502 2.64998 13.2532 2.64998 13.7502V16.0774L6.36358 12.3638C6.71505 12.0124 7.2849 12.0124 7.63637 12.3638Z" />
    <path d="M12.3636 12.3638C12.715 12.0124 13.2849 12.0124 13.6364 12.3638L17.35 16.0774V13.7502C17.35 13.2532 17.7529 12.8502 18.25 12.8502C18.747 12.8502 19.15 13.2532 19.15 13.7502V18.2502C19.15 18.7473 18.747 19.1502 18.25 19.1502H13.75C13.2529 19.1502 12.85 18.7473 12.85 18.2502C12.85 17.7532 13.2529 17.3502 13.75 17.3502H16.0772L12.3636 13.6366C12.0121 13.2851 12.0121 12.7153 12.3636 12.3638Z" />
  </svg>
);

export const ArchiveX = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="18"
    viewBox="0 0 22 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M2.375 0C1.33947 0 0.5 0.839466 0.5 1.875V2.625C0.5 3.66053 1.33947 4.5 2.375 4.5H19.625C20.6605 4.5 21.5 3.66053 21.5 2.625V1.875C21.5 0.839467 20.6605 0 19.625 0H2.375Z" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.08679 6L2.62657 15.1762C2.71984 16.7619 4.03296 18 5.62139 18H16.3783C17.9667 18 19.2799 16.7619 19.3731 15.1762L19.9129 6H2.08679ZM8.21967 8.84467C8.51256 8.55178 8.98744 8.55178 9.28033 8.84467L11 10.5643L12.7197 8.84467C13.0126 8.55178 13.4874 8.55178 13.7803 8.84467C14.0732 9.13756 14.0732 9.61244 13.7803 9.90533L12.0607 11.625L13.7803 13.3447C14.0732 13.6376 14.0732 14.1124 13.7803 14.4053C13.4874 14.6982 13.0126 14.6982 12.7197 14.4053L11 12.6857L9.28033 14.4053C8.98744 14.6982 8.51256 14.6982 8.21967 14.4053C7.92678 14.1124 7.92678 13.6376 8.21967 13.3447L9.93934 11.625L8.21967 9.90533C7.92678 9.61244 7.92678 9.13756 8.21967 8.84467Z"
    />
  </svg>
);

export const Calendar = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.75 2.25C7.16421 2.25 7.5 2.58579 7.5 3V4.5H16.5V3C16.5 2.58579 16.8358 2.25 17.25 2.25C17.6642 2.25 18 2.58579 18 3V4.5H18.75C20.4069 4.5 21.75 5.84315 21.75 7.5V18.75C21.75 20.4069 20.4069 21.75 18.75 21.75H5.25C3.59315 21.75 2.25 20.4069 2.25 18.75V7.5C2.25 5.84315 3.59315 4.5 5.25 4.5H6V3C6 2.58579 6.33579 2.25 6.75 2.25ZM20.25 11.25C20.25 10.4216 19.5784 9.75 18.75 9.75H5.25C4.42157 9.75 3.75 10.4216 3.75 11.25V18.75C3.75 19.5784 4.42157 20.25 5.25 20.25H18.75C19.5784 20.25 20.25 19.5784 20.25 18.75V11.25Z"
    />
  </svg>
);

export const PDF = ({ className }: { className?: string }) => (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.09961 2C3.27118 2 2.59961 2.67157 2.59961 3.5V12.5C2.59961 13.3284 3.27118 14 4.09961 14H12.0996C12.928 14 13.5996 13.3284 13.5996 12.5V6.62132C13.5996 6.2235 13.4416 5.84197 13.1603 5.56066L10.0389 2.43934C9.75764 2.15804 9.37611 2 8.97829 2H4.09961ZM11.0996 9C11.0996 9.41421 10.7638 9.75 10.3496 9.75H5.84961C5.4354 9.75 5.09961 9.41421 5.09961 9C5.09961 8.58579 5.4354 8.25 5.84961 8.25H10.3496C10.7638 8.25 11.0996 8.58579 11.0996 9Z"
      fill="#F43F5E"
    />
  </svg>
);

export const Figma = ({ className }: { className?: string }) => (
  <svg
    width="9"
    height="12"
    viewBox="0 0 9 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <g clipPath="url(#clip0_2827_15426)">
      <mask
        id="mask0_2827_15426"
        mask-type="luminance"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="9"
        height="12"
      >
        <path d="M8.00195 0H0.00195312V12H8.00195V0Z" fill="white" />
      </mask>
      <g mask="url(#mask0_2827_15426)">
        <path
          d="M2.00195 12C3.10595 12 4.00196 11.104 4.00196 10V8H2.00195C0.897953 8 0.00195312 8.896 0.00195312 10C0.00195312 11.104 0.897953 12 2.00195 12Z"
          fill="#0ACF83"
        />
        <path
          d="M0.00195312 6C0.00195312 4.896 0.897953 4 2.00195 4H4.00196V8H2.00195C0.897953 8 0.00195312 7.104 0.00195312 6Z"
          fill="#A259FF"
        />
        <path
          d="M0.00195312 1.99999C0.00195312 0.895999 0.897953 0 2.00195 0H4.00196V4.00001H2.00195C0.897953 4.00001 0.00195312 3.104 0.00195312 1.99999Z"
          fill="#F24E1E"
        />
        <path
          d="M4.00195 0H6.00196C7.10596 0 8.00196 0.895999 8.00196 1.99999C8.00196 3.104 7.10596 4.00001 6.00196 4.00001H4.00195V0Z"
          fill="#FF7262"
        />
        <path
          d="M8.00196 6C8.00196 7.104 7.10596 8 6.00196 8C4.89796 8 4.00195 7.104 4.00195 6C4.00195 4.896 4.89796 4 6.00196 4C7.10596 4 8.00196 4.896 8.00196 6Z"
          fill="#1ABCFE"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_2827_15426">
        <rect width="8.1" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Docx = ({ className }: { className?: string }) => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.09961 0C1.27118 0 0.599609 0.671573 0.599609 1.5V10.5C0.599609 11.3284 1.27118 12 2.09961 12H10.0996C10.928 12 11.5996 11.3284 11.5996 10.5V4.62132C11.5996 4.2235 11.4416 3.84197 11.1603 3.56066L8.03895 0.43934C7.75764 0.158035 7.37611 0 6.97829 0H2.09961ZM3.09961 5.75C3.09961 5.33579 3.4354 5 3.84961 5H8.34961C8.76382 5 9.09961 5.33579 9.09961 5.75C9.09961 6.16421 8.76382 6.5 8.34961 6.5H3.84961C3.4354 6.5 3.09961 6.16421 3.09961 5.75ZM3.09961 8.75C3.09961 8.33579 3.4354 8 3.84961 8H8.34961C8.76382 8 9.09961 8.33579 9.09961 8.75C9.09961 9.16421 8.76382 9.5 8.34961 9.5H3.84961C3.4354 9.5 3.09961 9.16421 3.09961 8.75Z"
      fill="#3B82F6"
    />
  </svg>
);

export const ImageFile = ({ className }: { className?: string }) => (
  <svg
    width="13"
    height="12"
    viewBox="0 0 13 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.0996094 2C0.0996094 0.895431 0.99504 0 2.09961 0H10.0996C11.2042 0 12.0996 0.895431 12.0996 2V10C12.0996 11.1046 11.2042 12 10.0996 12H2.09961C0.99504 12 0.0996094 11.1046 0.0996094 10V2ZM10.5996 7.70711C10.5996 7.5745 10.5469 7.44732 10.4532 7.35355L9.45316 6.35355C9.2579 6.15829 8.94132 6.15829 8.74606 6.35355L7.45316 7.64645C7.2579 7.84171 6.94132 7.84171 6.74606 7.64645L4.45316 5.35355C4.2579 5.15829 3.94132 5.15829 3.74606 5.35355L1.74606 7.35355C1.65229 7.44732 1.59961 7.5745 1.59961 7.70711V10C1.59961 10.2761 1.82347 10.5 2.09961 10.5H10.0996C10.3758 10.5 10.5996 10.2761 10.5996 10V7.70711ZM10.0996 3C10.0996 3.55228 9.65189 4 9.09961 4C8.54733 4 8.09961 3.55228 8.09961 3C8.09961 2.44772 8.54733 2 9.09961 2C9.65189 2 10.0996 2.44772 10.0996 3Z"
      fill="#8B5CF6"
    />
  </svg>
);

export const Smile = ({ className }: { className?: string }) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14 7C14 10.866 10.866 14 7 14C3.13401 14 0 10.866 0 7C0 3.13401 3.13401 0 7 0C10.866 0 14 3.13401 14 7ZM5 7C5.55228 7 6 6.32843 6 5.5C6 4.67157 5.55228 4 5 4C4.44772 4 4 4.67157 4 5.5C4 6.32843 4.44772 7 5 7ZM10 5.5C10 6.32843 9.55229 7 9 7C8.44771 7 8 6.32843 8 5.5C8 4.67157 8.44771 4 9 4C9.55229 4 10 4.67157 10 5.5ZM10.0052 9.74468C9.71231 9.45179 9.23744 9.45179 8.94454 9.74468C7.8706 10.8186 6.1294 10.8186 5.05546 9.74468C4.76256 9.45179 4.28769 9.45179 3.9948 9.74468C3.7019 10.0376 3.7019 10.5125 3.9948 10.8053C5.65452 12.4651 8.34548 12.4651 10.0052 10.8053C10.2981 10.5124 10.2981 10.0376 10.0052 9.74468Z"
    />
  </svg>
);

export const ShortStack = ({ className }: { className?: string }) => (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <g opacity="0.25">
      <path d="M0 1.5C0 1.08579 0.335786 0.75 0.75 0.75H9.25C9.66421 0.75 10 1.08579 10 1.5C10 1.91421 9.66421 2.25 9.25 2.25H0.75C0.335786 2.25 0 1.91421 0 1.5Z" />
    </g>
    <g opacity="0.25">
      <path d="M0 5C0 4.58579 0.335786 4.25 0.75 4.25H9.25C9.66421 4.25 10 4.58579 10 5C10 5.41421 9.66421 5.75 9.25 5.75H0.75C0.335786 5.75 0 5.41421 0 5Z" />
    </g>
    <path d="M0 8.5C0 8.08579 0.335786 7.75 0.75 7.75H9.25C9.66421 7.75 10 8.08579 10 8.5C10 8.91421 9.66421 9.25 9.25 9.25H0.75C0.335786 9.25 0 8.91421 0 8.5Z" />
  </svg>
);

export const MediumStack = ({ className }: { className?: string }) => (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <g opacity="0.25">
      <path d="M0 1.5C0 1.08579 0.335786 0.75 0.75 0.75H9.25C9.66421 0.75 10 1.08579 10 1.5C10 1.91421 9.66421 2.25 9.25 2.25H0.75C0.335786 2.25 0 1.91421 0 1.5Z" />
    </g>
    <path d="M0 5C0 4.58579 0.335786 4.25 0.75 4.25H9.25C9.66421 4.25 10 4.58579 10 5C10 5.41421 9.66421 5.75 9.25 5.75H0.75C0.335786 5.75 0 5.41421 0 5Z" />
    <path d="M0 8.5C0 8.08579 0.335786 7.75 0.75 7.75H9.25C9.66421 7.75 10 8.08579 10 8.5C10 8.91421 9.66421 9.25 9.25 9.25H0.75C0.335786 9.25 0 8.91421 0 8.5Z" />
  </svg>
);

export const LongStack = ({ className }: { className?: string }) => (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M0 1.5C0 1.08579 0.335786 0.75 0.75 0.75H9.25C9.66421 0.75 10 1.08579 10 1.5C10 1.91421 9.66421 2.25 9.25 2.25H0.75C0.335786 2.25 0 1.91421 0 1.5Z" />
    <path d="M0 5C0 4.58579 0.335786 4.25 0.75 4.25H9.25C9.66421 4.25 10 4.58579 10 5C10 5.41421 9.66421 5.75 9.25 5.75H0.75C0.335786 5.75 0 5.41421 0 5Z" />
    <path d="M0 8.5C0 8.08579 0.335786 7.75 0.75 7.75H9.25C9.66421 7.75 10 8.08579 10 8.5C10 8.91421 9.66421 9.25 9.25 9.25H0.75C0.335786 9.25 0 8.91421 0 8.5Z" />
  </svg>
);

export const Suitcase = ({ className }: { className?: string }) => (
  <svg
    width="12"
    height="13"
    viewBox="0 0 12 13"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9 3V2C9 0.895431 8.10457 0 7 0H5C3.89543 0 3 0.89543 3 2V3H2C0.895431 3 0 3.89543 0 5V8C0 9.10457 0.895431 10 2 10H10C11.1046 10 12 9.10457 12 8V5C12 3.89543 11.1046 3 10 3H9ZM7 1.5H5C4.72386 1.5 4.5 1.72386 4.5 2V3H7.5V2C7.5 1.72386 7.27614 1.5 7 1.5ZM7 8C7 8.55228 6.55228 9 6 9C5.44772 9 5 8.55228 5 8C5 7.44772 5.44772 7 6 7C6.55228 7 7 7.44772 7 8Z"
    />
    <path d="M1 10.8291V10.9998C1 12.1044 1.89543 12.9998 3 12.9998H9C10.1046 12.9998 11 12.1044 11 10.9998V10.8291C10.6872 10.9397 10.3506 10.9998 10 10.9998H2C1.64936 10.9998 1.31278 10.9397 1 10.8291Z" />
  </svg>
);

export const ChevronDown = ({ className }: { className?: string }) => (
  <svg
    width="8"
    height="6"
    viewBox="0 0 8 6"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      id="Union"
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.21967 0.84467C0.512563 0.551777 0.987437 0.551777 1.28033 0.84467L4 3.56434L6.71967 0.844671C7.01256 0.551778 7.48744 0.551778 7.78033 0.844671C8.07322 1.13756 8.07322 1.61244 7.78033 1.90533L4.53033 5.15533C4.23744 5.44822 3.76256 5.44822 3.46967 5.15533L0.21967 1.90533C-0.0732233 1.61244 -0.0732233 1.13756 0.21967 0.84467Z"
    />
  </svg>
);

export const LinkedIn = ({ className }: { className?: string }) => (
  <svg
    width="18"
    height="19"
    viewBox="0 0 18 19"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M5.20508 4.00075C5.20488 4.39857 5.04665 4.78003 4.76521 5.06119C4.48376 5.34235 4.10215 5.5002 3.70433 5.5C3.3065 5.4998 2.92505 5.34158 2.64389 5.06013C2.36272 4.77868 2.20488 4.39707 2.20508 3.99925C2.20528 3.60143 2.3635 3.21997 2.64495 2.93881C2.92639 2.65765 3.308 2.4998 3.70583 2.5C4.10365 2.5002 4.48511 2.65843 4.76627 2.93987C5.04743 3.22132 5.20528 3.60293 5.20508 4.00075ZM5.25008 6.61075H2.25008V16.0007H5.25008V6.61075ZM9.99008 6.61075H7.00508V16.0007H9.96008V11.0733C9.96008 8.32825 13.5376 8.07325 13.5376 11.0733V16.0007H16.5001V10.0533C16.5001 5.42575 11.2051 5.59825 9.96008 7.87075L9.99008 6.61075Z" />
  </svg>
);

export const People = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="22"
    height="20"
    viewBox="0 0 22 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M3.5 4.375C3.5 2.09683 5.34683 0.25 7.625 0.25C9.90317 0.25 11.75 2.09683 11.75 4.375C11.75 6.65317 9.90317 8.5 7.625 8.5C5.34683 8.5 3.5 6.65317 3.5 4.375Z" />
    <path d="M13.25 6.625C13.25 4.76104 14.761 3.25 16.625 3.25C18.489 3.25 20 4.76104 20 6.625C20 8.48896 18.489 10 16.625 10C14.761 10 13.25 8.48896 13.25 6.625Z" />
    <path d="M0.5 17.125C0.5 13.19 3.68997 10 7.625 10C11.56 10 14.75 13.19 14.75 17.125V17.1276C14.75 17.1674 14.7496 17.2074 14.749 17.2469C14.7446 17.5054 14.6074 17.7435 14.3859 17.8768C12.4107 19.0661 10.0966 19.75 7.625 19.75C5.15343 19.75 2.8393 19.0661 0.864061 17.8768C0.642563 17.7435 0.505373 17.5054 0.501026 17.2469C0.500345 17.2064 0.5 17.1657 0.5 17.125Z" />
    <path d="M16.2498 17.1281C16.2498 17.1762 16.2494 17.2244 16.2486 17.2722C16.2429 17.6108 16.1612 17.9378 16.0157 18.232C16.2172 18.2439 16.4203 18.25 16.6248 18.25C18.2206 18.25 19.732 17.8803 21.0764 17.2213C21.3234 17.1002 21.4843 16.8536 21.4957 16.5787C21.4984 16.5111 21.4998 16.4432 21.4998 16.375C21.4998 13.6826 19.3172 11.5 16.6248 11.5C15.8784 11.5 15.1711 11.6678 14.5387 11.9676C15.6135 13.4061 16.2498 15.1912 16.2498 17.125V17.1281Z" />
  </svg>
);

export const Star2 = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.99934 1.75C8.30229 1.75 8.57549 1.93226 8.69183 2.21198L10.1029 5.60466L13.7656 5.8983C14.0676 5.92251 14.3254 6.12601 14.419 6.41413C14.5126 6.70226 14.4237 7.01841 14.1936 7.21549L11.403 9.60592L12.2556 13.1801C12.3259 13.4748 12.212 13.7828 11.9669 13.9609C11.7218 14.1389 11.3936 14.1521 11.1351 13.9942L7.99934 12.0788L4.86357 13.9942C4.60503 14.1521 4.27688 14.1389 4.03179 13.9609C3.7867 13.7828 3.6728 13.4748 3.7431 13.1801L4.59566 9.60592L1.80508 7.21549C1.57501 7.01841 1.48609 6.70226 1.57971 6.41413C1.67333 6.12601 1.93109 5.92251 2.23307 5.8983L5.89575 5.60466L7.30685 2.21198C7.42319 1.93226 7.69639 1.75 7.99934 1.75Z"
    />
  </svg>
);

export const Archive2 = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M3 2C2.44772 2 2 2.44772 2 3V4C2 4.55228 2.44772 5 3 5H13C13.5523 5 14 4.55228 14 4V3C14 2.44772 13.5523 2 13 2H3Z" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3 6H13V12C13 13.1046 12.1046 14 11 14H5C3.89543 14 3 13.1046 3 12V6ZM6 8.75C6 8.33579 6.33579 8 6.75 8H9.25C9.66421 8 10 8.33579 10 8.75C10 9.16421 9.66421 9.5 9.25 9.5H6.75C6.33579 9.5 6 9.16421 6 8.75Z"
    />
  </svg>
);

export const InfoCircle = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12ZM10.9562 10.5584C12.1025 9.98533 13.3931 11.0206 13.0823 12.2639L12.3733 15.0999L12.4148 15.0792C12.7852 14.894 13.2357 15.0441 13.421 15.4146C13.6062 15.7851 13.4561 16.2356 13.0856 16.4208L13.0441 16.4416C11.8979 17.0147 10.6072 15.9794 10.9181 14.7361L11.6271 11.9001L11.5856 11.9208C11.2151 12.1061 10.7646 11.9559 10.5793 11.5854C10.3941 11.2149 10.5443 10.7644 10.9148 10.5792L10.9562 10.5584ZM12 9C12.4142 9 12.75 8.66421 12.75 8.25C12.75 7.83579 12.4142 7.5 12 7.5C11.5858 7.5 11.25 7.83579 11.25 8.25C11.25 8.66421 11.5858 9 12 9Z"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.08679 6L2.62657 15.1762C2.71984 16.7619 4.03296 18 5.62139 18H16.3783C17.9667 18 19.2799 16.7619 19.3731 15.1762L19.9129 6H2.08679ZM8.24976 9.75C8.24976 9.33579 8.58554 9 8.99976 9H12.9998C13.414 9 13.7498 9.33579 13.7498 9.75C13.7498 10.1642 13.414 10.5 12.9998 10.5H8.99976C8.58554 10.5 8.24976 10.1642 8.24976 9.75Z"
    />
  </svg>
);

export const Pencil = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M18.5134 7.19897L14.801 3.48666L2.65021 15.6375C2.03342 16.2543 1.58003 17.015 1.33101 17.851L0.531232 20.5359C0.452612 20.7998 0.524959 21.0856 0.719688 21.2803C0.914418 21.4751 1.2002 21.5474 1.46413 21.4688L4.14902 20.669C4.98499 20.42 5.74574 19.9666 6.36253 19.3498L18.5134 7.19897Z" />
  </svg>
);

export const NotesList = ({ className }: { className?: string }) => (
  <svg
    width="18"
    height="22"
    viewBox="0 0 18 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.50183 5H8.24766L8.25 5H11.6299C13.4915 5.00268 14.9999 6.51269 14.9999 8.375V17.75C16.6567 17.75 17.9999 16.4068 17.9999 14.75V5.10821C17.9999 3.60282 16.8751 2.2966 15.3358 2.16884C15.1121 2.15027 14.8879 2.13321 14.6632 2.11767C14.1633 1.15647 13.1583 0.5 12 0.5H10.5C9.34168 0.5 8.33668 1.15647 7.83676 2.11765C7.61207 2.13319 7.38779 2.15026 7.16394 2.16884C5.66165 2.29353 4.55421 3.54069 4.50183 5ZM10.5 2C9.67157 2 9 2.67157 9 3.5H13.5C13.5 2.67157 12.8284 2 12 2H10.5Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 8.375C0 7.33947 0.839466 6.5 1.875 6.5H11.625C12.6605 6.5 13.5 7.33947 13.5 8.375V19.625C13.5 20.6605 12.6605 21.5 11.625 21.5H1.875C0.839466 21.5 0 20.6605 0 19.625V8.375ZM3 11C3 10.5858 3.33579 10.25 3.75 10.25H3.7575C4.17171 10.25 4.5075 10.5858 4.5075 11V11.0075C4.5075 11.4217 4.17171 11.7575 3.7575 11.7575H3.75C3.33579 11.7575 3 11.4217 3 11.0075V11ZM5.25 11C5.25 10.5858 5.58579 10.25 6 10.25H9.75C10.1642 10.25 10.5 10.5858 10.5 11C10.5 11.4142 10.1642 11.75 9.75 11.75H6C5.58579 11.75 5.25 11.4142 5.25 11ZM3 14C3 13.5858 3.33579 13.25 3.75 13.25H3.7575C4.17171 13.25 4.5075 13.5858 4.5075 14V14.0075C4.5075 14.4217 4.17171 14.7575 3.7575 14.7575H3.75C3.33579 14.7575 3 14.4217 3 14.0075V14ZM5.25 14C5.25 13.5858 5.58579 13.25 6 13.25H9.75C10.1642 13.25 10.5 13.5858 10.5 14C10.5 14.4142 10.1642 14.75 9.75 14.75H6C5.58579 14.75 5.25 14.4142 5.25 14ZM3 17C3 16.5858 3.33579 16.25 3.75 16.25H3.7575C4.17171 16.25 4.5075 16.5858 4.5075 17V17.0075C4.5075 17.4217 4.17171 17.7575 3.7575 17.7575H3.75C3.33579 17.7575 3 17.4217 3 17.0075V17ZM5.25 17C5.25 16.5858 5.58579 16.25 6 16.25H9.75C10.1642 16.25 10.5 16.5858 10.5 17C10.5 17.4142 10.1642 17.75 9.75 17.75H6C5.58579 17.75 5.25 17.4142 5.25 17Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Stars = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8 3.5C8.33486 3.5 8.62915 3.72198 8.72114 4.04396L9.53434 6.89015C9.89028 8.13593 10.8641 9.10972 12.1099 9.46566L14.956 10.2789C15.278 10.3709 15.5 10.6651 15.5 11C15.5 11.3349 15.278 11.6291 14.956 11.7211L12.1098 12.5343C10.8641 12.8903 9.89028 13.8641 9.53434 15.1099L8.72114 17.956C8.62915 18.278 8.33486 18.5 8 18.5C7.66514 18.5 7.37085 18.278 7.27886 17.956L6.46566 15.1099C6.10972 13.8641 5.13593 12.8903 3.89015 12.5343L1.04396 11.7211C0.721983 11.6291 0.5 11.3349 0.5 11C0.5 10.6651 0.721983 10.3709 1.04396 10.2789L3.89015 9.46566C5.13593 9.10972 6.10972 8.13593 6.46566 6.89015L7.27886 4.04396C7.37085 3.72198 7.66514 3.5 8 3.5Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17 0.5C17.3442 0.5 17.6441 0.734223 17.7276 1.0681L17.9865 2.10356C18.2216 3.04406 18.9559 3.7784 19.8964 4.01353L20.9319 4.27239C21.2658 4.35586 21.5 4.65585 21.5 5C21.5 5.34415 21.2658 5.64414 20.9319 5.72761L19.8964 5.98647C18.9559 6.2216 18.2216 6.95594 17.9865 7.89644L17.7276 8.9319C17.6441 9.26578 17.3442 9.5 17 9.5C16.6558 9.5 16.3559 9.26578 16.2724 8.9319L16.0135 7.89644C15.7784 6.95594 15.0441 6.2216 14.1036 5.98647L13.0681 5.72761C12.7342 5.64414 12.5 5.34415 12.5 5C12.5 4.65585 12.7342 4.35586 13.0681 4.27239L14.1036 4.01353C15.0441 3.7784 15.7784 3.04406 16.0135 2.10356L16.2724 1.0681C16.3559 0.734223 16.6558 0.5 17 0.5Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.5 14C15.8228 14 16.1094 14.2066 16.2115 14.5128L16.6058 15.6956C16.7551 16.1435 17.1065 16.4949 17.5544 16.6442L18.7372 17.0385C19.0434 17.1406 19.25 17.4272 19.25 17.75C19.25 18.0728 19.0434 18.3594 18.7372 18.4615L17.5544 18.8558C17.1065 19.0051 16.7551 19.3565 16.6058 19.8044L16.2115 20.9872C16.1094 21.2934 15.8228 21.5 15.5 21.5C15.1772 21.5 14.8906 21.2934 14.7885 20.9872L14.3942 19.8044C14.2449 19.3565 13.8935 19.0051 13.4456 18.8558L12.2628 18.4615C11.9566 18.3594 11.75 18.0728 11.75 17.75C11.75 17.4272 11.9566 17.1406 12.2628 17.0385L13.4456 16.6442C13.8935 16.4949 14.2449 16.1435 14.3942 15.6956L14.7885 14.5128C14.8906 14.2066 15.1772 14 15.5 14Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Danger = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.4843 2.16968C11.7735 1.89575 12.2265 1.89575 12.5157 2.16968C14.5325 4.07965 17.2538 5.24998 20.25 5.24998C20.2977 5.24998 20.3453 5.24969 20.3928 5.2491C20.7202 5.24503 21.0123 5.45378 21.1146 5.76479C21.5271 7.01955 21.75 8.35951 21.75 9.75003C21.75 15.692 17.6859 20.683 12.1869 22.0983C12.0643 22.1299 11.9357 22.1299 11.8131 22.0983C6.31406 20.683 2.25 15.692 2.25 9.75003C2.25 8.35951 2.47287 7.01955 2.88541 5.76479C2.98767 5.45378 3.27984 5.24503 3.60721 5.2491C3.65473 5.24969 3.70233 5.24998 3.75 5.24998C6.74624 5.24998 9.46752 4.07965 11.4843 2.16968ZM12 8.24997C12.4142 8.24997 12.75 8.58576 12.75 8.99997V12.75C12.75 13.1642 12.4142 13.5 12 13.5C11.5858 13.5 11.25 13.1642 11.25 12.75V8.99997C11.25 8.58576 11.5858 8.24997 12 8.24997ZM12 15C11.5858 15 11.25 15.3358 11.25 15.75V15.7575C11.25 16.1717 11.5858 16.5075 12 16.5075H12.0075C12.4217 16.5075 12.7575 16.1717 12.7575 15.7575V15.75C12.7575 15.3358 12.4217 15 12.0075 15H12Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Important = ({ className }: { className?: string }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8 14.5C11.5899 14.5 14.5 11.5899 14.5 8C14.5 4.41015 11.5899 1.5 8 1.5C4.41015 1.5 1.5 4.41015 1.5 8C1.5 11.5899 4.41015 14.5 8 14.5ZM8 4.28571C8.38463 4.28571 8.69643 4.59752 8.69643 4.98214V7.76786C8.69643 8.15248 8.38463 8.46429 8 8.46429C7.61537 8.46429 7.30357 8.15248 7.30357 7.76786V4.98214C7.30357 4.59752 7.61537 4.28571 8 4.28571ZM8 11.7143C8.51284 11.7143 8.92857 11.2986 8.92857 10.7857C8.92857 10.2729 8.51284 9.85714 8 9.85714C7.48716 9.85714 7.07143 10.2729 7.07143 10.7857C7.07143 11.2986 7.48716 11.7143 8 11.7143Z"
    />
  </svg>
);

export const Eye = ({ className }: { className?: string }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    height="16"
    viewBox="0 0 16 16"
    width="16"
  >
    <g>
      <path d="m8 9.5c.82843 0 1.5-.67157 1.5-1.5s-.67157-1.5-1.5-1.5-1.5.67157-1.5 1.5.67157 1.5 1.5 1.5z" />
      <path
        clipRule="evenodd"
        d="m1.3794 8.28049c-.06324-.18362-.06315-.38322.00025-.56678.94754-2.74333 3.55273-4.71371 6.61812-4.71371 3.06753 0 5.67423 1.97316 6.62013 4.71951.0632.18362.0631.38323-.0003.56679-.9475 2.7433-3.5527 4.7137-6.61808 4.7137-3.06755 0-5.67425-1.9732-6.62012-4.71951zm9.6206-.28049c0 1.65685-1.34315 3-3 3s-3-1.34315-3-3 1.34315-3 3-3 3 1.34315 3 3z"
        fillRule="evenodd"
      />
    </g>
  </svg>
);

export const ExclamationCircle2 = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="20"
    height="20"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7 13.5C10.5899 13.5 13.5 10.5899 13.5 7C13.5 3.41015 10.5899 0.5 7 0.5C3.41015 0.5 0.5 3.41015 0.5 7C0.5 10.5899 3.41015 13.5 7 13.5ZM7 3.28571C7.38463 3.28571 7.69643 3.59752 7.69643 3.98214V6.76786C7.69643 7.15248 7.38463 7.46429 7 7.46429C6.61537 7.46429 6.30357 7.15248 6.30357 6.76786V3.98214C6.30357 3.59752 6.61537 3.28571 7 3.28571ZM7 10.7143C7.51284 10.7143 7.92857 10.2986 7.92857 9.78571C7.92857 9.27288 7.51284 8.85714 7 8.85714C6.48716 8.85714 6.07143 9.27288 6.07143 9.78571C6.07143 10.2986 6.48716 10.7143 7 10.7143Z"
    />
  </svg>
);

export const Sheet = ({ className }: { className?: string }) => (
  <svg
    width="18"
    height="22"
    viewBox="0 0 18 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.625 0.5C1.58947 0.5 0.75 1.33947 0.75 2.375V19.625C0.75 20.6605 1.58947 21.5 2.625 21.5H15.375C16.4105 21.5 17.25 20.6605 17.25 19.625V11.75C17.25 9.67893 15.5711 8 13.5 8H11.625C10.5895 8 9.75 7.16053 9.75 6.125V4.25C9.75 2.17893 8.07107 0.5 6 0.5H2.625ZM4.5 14C4.5 13.5858 4.83579 13.25 5.25 13.25H12.75C13.1642 13.25 13.5 13.5858 13.5 14C13.5 14.4142 13.1642 14.75 12.75 14.75H5.25C4.83579 14.75 4.5 14.4142 4.5 14ZM5.25 16.25C4.83579 16.25 4.5 16.5858 4.5 17C4.5 17.4142 4.83579 17.75 5.25 17.75H9C9.41421 17.75 9.75 17.4142 9.75 17C9.75 16.5858 9.41421 16.25 9 16.25H5.25Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M9.97119 0.815905C10.768 1.73648 11.25 2.93695 11.25 4.25V6.125C11.25 6.33211 11.4179 6.5 11.625 6.5H13.5C14.8131 6.5 16.0135 6.98204 16.9341 7.77881C16.0462 4.37988 13.3701 1.70377 9.97119 0.815905Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Plane2 = ({ className }: { className?: string }) => (
  <svg
    width="17"
    height="16"
    viewBox="-2 -1 19 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M1.10526 0.288438C0.853622 0.252772 0.601043 0.346971 0.434213 0.538704C0.267382 0.730436 0.209003 0.993612 0.279111 1.2379L1.69276 6.16378C1.87733 6.80688 2.4655 7.25 3.13456 7.25H9.25002C9.66423 7.25 10 7.58579 10 8C10 8.41421 9.66423 8.75 9.25002 8.75H3.13457C2.4655 8.75 1.87733 9.19312 1.69277 9.83622L0.279111 14.7621C0.209003 15.0064 0.267382 15.2696 0.434213 15.4613C0.601043 15.6531 0.853622 15.7473 1.10526 15.7116C6.94303 14.8842 12.221 12.3187 16.3983 8.55737C16.5563 8.41514 16.6465 8.21257 16.6465 8.00001C16.6465 7.78746 16.5563 7.58489 16.3983 7.44266C12.221 3.68129 6.94303 1.11585 1.10526 0.288438Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const Pencil2 = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="22"
    viewBox="-4 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M20.7312 1.26884C19.706 0.243718 18.044 0.243718 17.0189 1.26884L15.8617 2.426L19.574 6.13831L20.7312 4.98116C21.7563 3.95603 21.7563 2.29397 20.7312 1.26884Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
    <path
      d="M18.5134 7.19897L14.801 3.48666L2.65021 15.6375C2.03342 16.2543 1.58003 17.015 1.33101 17.851L0.531232 20.5359C0.452612 20.7998 0.524959 21.0856 0.719688 21.2803C0.914418 21.4751 1.2002 21.5474 1.46413 21.4688L4.14902 20.669C4.98499 20.42 5.74574 19.9666 6.36253 19.3498L18.5134 7.19897Z"
      fill="var(--icon-color)"
      fillOpacity="0.5"
    />
  </svg>
);

export const ArrowRight = ({ className }: { className?: string }) => (
  <svg
    width="13"
    height="10"
    viewBox="0 0 13 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M1.16669 5H11.8334M11.8334 5L7.83335 1M11.8334 5L7.83335 9"
      stroke="white"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const OpenMail = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M18.5 21.5C20.1569 21.5 21.5 20.1568 21.5 18.5V10.3262L14.6212 14.3481L18.1056 16.2243C18.4703 16.4206 18.6067 16.8755 18.4104 17.2402C18.214 17.6049 17.7591 17.7413 17.3944 17.545L11.7112 14.4847C11.2672 14.2457 10.7328 14.2457 10.2889 14.4847L4.60558 17.545C4.24087 17.7413 3.78603 17.6049 3.58965 17.2402C3.39327 16.8755 3.52972 16.4206 3.89442 16.2243L7.37878 14.3481L0.5 10.3262V18.5C0.5 20.1568 1.84315 21.5 3.5 21.5L18.5 21.5Z" />
    <path d="M0.5 8.58861V7.84388C0.5 6.74023 1.10597 5.72571 2.0777 5.20247L9.5777 1.16401C10.4656 0.685891 11.5344 0.685891 12.4223 1.16401L19.9223 5.20247C20.894 5.72571 21.5 6.74024 21.5 7.84388V8.58861L13.0742 13.515L12.4223 13.164C11.5344 12.6859 10.4656 12.6859 9.5777 13.164L8.92585 13.515L0.5 8.58861Z" />
  </svg>
);

export const Paper = ({ className }: { className?: string }) => (
  <svg
    width="18"
    height="22"
    viewBox="1 -3 14 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M2.625 0.5C1.58947 0.5 0.75 1.33947 0.75 2.375V19.625C0.75 20.6605 1.58947 21.5 2.625 21.5H15.375C16.4105 21.5 17.25 20.6605 17.25 19.625V11.75C17.25 9.67893 15.5711 8 13.5 8H11.625C10.5895 8 9.75 7.16053 9.75 6.125V4.25C9.75 2.17893 8.07107 0.5 6 0.5H2.625Z" />
    <path d="M9.97119 0.815905C10.768 1.73648 11.25 2.93695 11.25 4.25V6.125C11.25 6.33211 11.4179 6.5 11.625 6.5H13.5C14.8131 6.5 16.0135 6.98204 16.9341 7.77881C16.0462 4.37988 13.3701 1.70377 9.97119 0.815905Z" />
  </svg>
);

export const Puzzle = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="21"
    height="21"
    viewBox="0 0 21 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M10.25 4.33694C10.25 3.98178 10.064 3.66076 9.8488 3.3782C9.62791 3.0881 9.5 2.744 9.5 2.375C9.5 1.33947 10.5074 0.5 11.75 0.5C12.9926 0.5 14 1.33947 14 2.375C14 2.744 13.8721 3.0881 13.6512 3.3782C13.436 3.66076 13.25 3.98178 13.25 4.33694C13.25 4.66929 13.5277 4.9346 13.8595 4.9148C15.7701 4.80079 17.6498 4.57328 19.4922 4.23898C19.6949 4.20219 19.9039 4.25044 20.07 4.37241C20.2361 4.49437 20.3447 4.6793 20.3703 4.88377C20.5943 6.67324 20.7213 8.49263 20.7459 10.3365C20.7508 10.7028 20.4533 10.9999 20.0869 11C19.7318 11 19.4108 10.814 19.1282 10.5988C18.8381 10.3779 18.494 10.25 18.125 10.25C17.0895 10.25 16.25 11.2574 16.25 12.5C16.25 13.7426 17.0895 14.75 18.125 14.75C18.494 14.75 18.8381 14.6221 19.1282 14.4012C19.4108 14.186 19.7318 14 20.0869 14C20.3974 14 20.6439 14.2617 20.6214 14.5713C20.5028 16.2098 20.3031 17.8261 20.0263 19.4161C19.9721 19.728 19.7279 19.9721 19.416 20.0264C17.5969 20.343 15.7434 20.5587 13.8615 20.6677C13.5285 20.6869 13.25 20.4205 13.25 20.0869C13.25 19.7318 13.436 19.4108 13.6512 19.1282C13.8721 18.8381 14 18.494 14 18.125C14 17.0895 12.9926 16.25 11.75 16.25C10.5074 16.25 9.5 17.0895 9.5 18.125C9.5 18.494 9.62791 18.8381 9.8488 19.1282C10.064 19.4108 10.25 19.7318 10.25 20.0869C10.25 20.4485 9.95396 20.7405 9.59252 20.7303C8.00303 20.6852 6.43238 20.564 4.88413 20.3702C4.67966 20.3446 4.49473 20.236 4.37277 20.0699C4.25081 19.9038 4.20256 19.6948 4.23935 19.4921C4.53223 17.8781 4.74315 16.2354 4.8676 14.5683C4.89058 14.2605 4.64563 14 4.33694 14C3.98178 14 3.66076 14.186 3.3782 14.4012C3.0881 14.6221 2.744 14.75 2.375 14.75C1.33947 14.75 0.5 13.7426 0.5 12.5C0.5 11.2574 1.33947 10.25 2.375 10.25C2.744 10.25 3.0881 10.3779 3.3782 10.5988C3.66076 10.814 3.98178 11 4.33694 11C4.7033 11 5.00078 10.703 4.99574 10.3367C4.97334 8.70845 4.86862 7.10026 4.68559 5.51628C4.6593 5.28881 4.73838 5.06178 4.9003 4.89986C5.06222 4.73794 5.28924 4.65886 5.51672 4.68515C6.85902 4.84025 8.2186 4.93912 9.59307 4.97931C9.95415 4.98987 10.25 4.69817 10.25 4.33694Z" />
  </svg>
);
export const ScanEye = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="20"
    viewBox="0 0 22 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M2.53033 0.46967C2.23744 0.176777 1.76256 0.176777 1.46967 0.46967C1.17678 0.762563 1.17678 1.23744 1.46967 1.53033L19.4697 19.5303C19.7626 19.8232 20.2374 19.8232 20.5303 19.5303C20.8232 19.2374 20.8232 18.7626 20.5303 18.4697L2.53033 0.46967Z" />
    <path d="M21.6759 10.5533C21.1319 12.1887 20.2226 13.6575 19.0447 14.8627L15.9462 11.7642C16.1429 11.2129 16.25 10.6189 16.25 10C16.25 7.1005 13.8995 4.75 11 4.75C10.3811 4.75 9.78715 4.8571 9.23577 5.05379L6.75898 2.577C8.06783 2.04381 9.49977 1.75 11.0005 1.75C15.9708 1.75 20.1864 4.97271 21.6755 9.44045C21.7959 9.80152 21.796 10.1922 21.6759 10.5533Z" />
    <path d="M14.75 10C14.75 10.1802 14.7373 10.3574 14.7127 10.5308L10.4692 6.28727C10.6426 6.2627 10.8198 6.25 11 6.25C13.0711 6.25 14.75 7.92893 14.75 10Z" />
    <path d="M11.5307 13.7127L7.28727 9.46925C7.2627 9.64263 7.25 9.81983 7.25 10C7.25 12.0711 8.92893 13.75 11 13.75C11.1802 13.75 11.3574 13.7373 11.5307 13.7127Z" />
    <path d="M5.75 10C5.75 9.38107 5.8571 8.78715 6.05379 8.23577L2.95492 5.1369C1.77687 6.34222 0.867472 7.81114 0.323411 9.4467C0.203283 9.80783 0.203397 10.1985 0.323739 10.5595C1.81284 15.0273 6.02847 18.25 10.9988 18.25C12.4997 18.25 13.9318 17.9561 15.2408 17.4228L12.7642 14.9462C12.2129 15.1429 11.6189 15.25 11 15.25C8.1005 15.25 5.75 12.8995 5.75 10Z" />
  </svg>
);

export const Plus = ({ className }: { className?: string }) => (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M5 0C5.41421 0 5.75 0.335786 5.75 0.75V4.25H9.25C9.66421 4.25 10 4.58579 10 5C10 5.41421 9.66421 5.75 9.25 5.75H5.75V9.25C5.75 9.66421 5.41421 10 5 10C4.58579 10 4.25 9.66421 4.25 9.25V5.75H0.75C0.335786 5.75 0 5.41421 0 5C0 4.58579 0.335786 4.25 0.75 4.25H4.25V0.75C4.25 0.335786 4.58579 0 5 0Z" />
  </svg>
);

export const Cube = ({ className }: { className?: string }) => (
  <svg
    width="12"
    height="14"
    viewBox="0 0 12 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M6.3721 0.348818C6.14153 0.217061 5.85847 0.217061 5.6279 0.348818L0.817939 3.09736L5.99998 6.13075L11.182 3.09735L6.3721 0.348818Z" />
    <path d="M12 4.35664L6.74998 7.42982V13.4353L11.6221 10.6512C11.8558 10.5177 12 10.2691 12 10V4.35664Z" />
    <path d="M5.24998 13.4352V7.42982L0 4.35666V10C0 10.2691 0.144215 10.5177 0.377896 10.6512L5.24998 13.4352Z" />
  </svg>
);

export const Clock = ({ className }: { className?: string }) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 7C0 3.13401 3.13401 0 7 0C10.866 0 14 3.13401 14 7C14 10.866 10.866 14 7 14C3.13401 14 0 10.866 0 7ZM7.75 2.75C7.75 2.33579 7.41421 2 7 2C6.58579 2 6.25 2.33579 6.25 2.75V7C6.25 7.41421 6.58579 7.75 7 7.75H10.25C10.6642 7.75 11 7.41421 11 7C11 6.58579 10.6642 6.25 10.25 6.25H7.75V2.75Z"
    />
  </svg>
);

export const Check = ({ className }: { className?: string }) => (
  <svg
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      id="Union"
      d="M6.40907 0.569119C6.58341 0.30762 6.93661 0.236654 7.19813 0.410916C7.45962 0.585241 7.53057 0.938456 7.35634 1.19998L3.56239 6.89138C3.46781 7.03326 3.31411 7.12556 3.14442 7.14236C2.97475 7.15915 2.806 7.09886 2.68544 6.9783L0.409072 4.70193C0.18694 4.47978 0.187156 4.11953 0.409072 3.89724C0.63133 3.67499 0.991493 3.675 1.21376 3.89724L2.99989 5.6824L6.40907 0.569119Z"
      fill="currentColor"
    />
  </svg>
);

export const Search = ({ className }: { className?: string }) => (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.81038 7.0756C6.18079 7.54011 5.40248 7.81466 4.56004 7.81466C2.46451 7.81466 0.765747 6.11589 0.765747 4.02037C0.765747 1.92484 2.46451 0.226074 4.56004 0.226074C6.65557 0.226074 8.35433 1.92484 8.35433 4.02037C8.35433 4.8628 8.07978 5.64112 7.61527 6.27071L9.70535 8.36078C9.92761 8.58305 9.92761 8.94341 9.70535 9.16568C9.48308 9.38794 9.12272 9.38794 8.90046 9.16568L6.81038 7.0756ZM7.21604 4.02037C7.21604 5.48724 6.02691 6.67637 4.56004 6.67637C3.09317 6.67637 1.90403 5.48724 1.90403 4.02037C1.90403 2.5535 3.09317 1.36436 4.56004 1.36436C6.02691 1.36436 7.21604 2.5535 7.21604 4.02037Z"
    />
  </svg>
);

export const Folders = ({ className }: { className?: string }) => (
  <svg
    width="20"
    height="18"
    viewBox="0 0 20 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M0.273071 2.62524C1.06638 1.92494 2.10851 1.5 3.24989 1.5H16.7499C17.8913 1.5 18.9334 1.92494 19.7267 2.62524C19.5423 1.14526 18.2798 0 16.7499 0H3.24989C1.71995 0 0.457504 1.14525 0.273071 2.62524Z" />
    <path d="M0.273071 5.62524C1.06638 4.92494 2.10851 4.5 3.24989 4.5H16.7499C17.8913 4.5 18.9334 4.92494 19.7267 5.62524C19.5423 4.14526 18.2798 3 16.7499 3H3.24989C1.71995 3 0.457504 4.14525 0.273071 5.62524Z" />
    <path d="M3.25 6C1.59315 6 0.25 7.34315 0.25 9V15C0.25 16.6569 1.59315 18 3.25 18H16.75C18.4069 18 19.75 16.6569 19.75 15V9C19.75 7.34315 18.4069 6 16.75 6H13C12.5858 6 12.25 6.33579 12.25 6.75C12.25 7.99264 11.2426 9 10 9C8.75736 9 7.75 7.99264 7.75 6.75C7.75 6.33579 7.41421 6 7 6H3.25Z" />
  </svg>
);

export const ArrowsPointingIn = ({ className }: { className?: string }) => (
  <svg width="20" height="20" viewBox="0 -1 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      className={className}
      d="M0.219671 0.21967C0.512564 -0.0732233 0.987438 -0.0732233 1.28033 0.21967L5.25 4.18934L5.25 1.5C5.25 1.08579 5.58579 0.75 6 0.75C6.41421 0.75 6.75 1.08579 6.75 1.5L6.75 6C6.75 6.41421 6.41421 6.75 6 6.75H1.5C1.08579 6.75 0.75 6.41421 0.75 6C0.75 5.58579 1.08579 5.25 1.5 5.25L4.18934 5.25L0.219671 1.28033C-0.0732224 0.987437 -0.0732223 0.512563 0.219671 0.21967ZM17.7803 0.21967C18.0732 0.512564 18.0732 0.987437 17.7803 1.28033L13.8107 5.25H16.5C16.9142 5.25 17.25 5.58579 17.25 6C17.25 6.41421 16.9142 6.75 16.5 6.75H12C11.5858 6.75 11.25 6.41421 11.25 6V1.5C11.25 1.08579 11.5858 0.75 12 0.75C12.4142 0.75 12.75 1.08579 12.75 1.5V4.18934L16.7197 0.21967C17.0126 -0.0732228 17.4874 -0.0732228 17.7803 0.21967ZM0.75 12C0.75 11.5858 1.08579 11.25 1.5 11.25L6 11.25C6.41421 11.25 6.75 11.5858 6.75 12V16.5C6.75 16.9142 6.41421 17.25 6 17.25C5.58579 17.25 5.25 16.9142 5.25 16.5L5.25 13.8107L1.28033 17.7803C0.987437 18.0732 0.512563 18.0732 0.21967 17.7803C-0.0732233 17.4874 -0.0732233 17.0126 0.21967 16.7197L4.18934 12.75H1.5C1.08579 12.75 0.75 12.4142 0.75 12ZM11.25 12C11.25 11.5858 11.5858 11.25 12 11.25H16.5C16.9142 11.25 17.25 11.5858 17.25 12C17.25 12.4142 16.9142 12.75 16.5 12.75H13.8107L17.7803 16.7197C18.0732 17.0126 18.0732 17.4874 17.7803 17.7803C17.4874 18.0732 17.0126 18.0732 16.7197 17.7803L12.75 13.8107V16.5C12.75 16.9142 12.4142 17.25 12 17.25C11.5858 17.25 11.25 16.9142 11.25 16.5L11.25 12Z"
    />
  </svg>
);
export const ThickCheck = ({ className }: { className?: string }) => (
  <svg
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M0.666992 4L2.75033 6.08333L7.33366 1.5" stroke="white" strokeWidth="1.875" />
  </svg>
);

export const ArrowsPointingOut = ({ className }: { className?: string }) => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      className={className}
      d="M12 0.75C12 0.335786 12.3358 0 12.75 0L17.25 1.78814e-07C17.6642 2.23517e-07 18 0.335787 18 0.75V5.25C18 5.66421 17.6642 6 17.25 6C16.8358 6 16.5 5.66421 16.5 5.25V2.56066L12.5303 6.53033C12.2374 6.82322 11.7626 6.82322 11.4697 6.53033C11.1768 6.23744 11.1768 5.76256 11.4697 5.46967L15.4393 1.5H12.75C12.3358 1.5 12 1.16421 12 0.75ZM0 0.75C0 0.335787 0.335786 1.78814e-07 0.75 1.78814e-07H5.25C5.66421 1.78814e-07 6 0.335787 6 0.75C6 1.16421 5.66421 1.5 5.25 1.5H2.56066L6.53033 5.46967C6.82322 5.76256 6.82322 6.23744 6.53033 6.53033C6.23744 6.82322 5.76256 6.82322 5.46967 6.53033L1.5 2.56066V5.25C1.5 5.66421 1.16421 6 0.75 6C0.335786 6 0 5.66421 0 5.25V0.75ZM11.4697 12.5303C11.1768 12.2374 11.1768 11.7626 11.4697 11.4697C11.7626 11.1768 12.2374 11.1768 12.5303 11.4697L16.5 15.4393V12.75C16.5 12.3358 16.8358 12 17.25 12C17.6642 12 18 12.3358 18 12.75V17.25C18 17.6642 17.6642 18 17.25 18H12.75C12.3358 18 12 17.6642 12 17.25C12 16.8358 12.3358 16.5 12.75 16.5H15.4393L11.4697 12.5303ZM6.53033 11.4697C6.82322 11.7626 6.82322 12.2374 6.53033 12.5303L2.56066 16.5H5.25C5.66421 16.5 6 16.8358 6 17.25C6 17.6642 5.66421 18 5.25 18H0.75C0.335786 18 0 17.6642 0 17.25V12.75C0 12.3358 0.335786 12 0.75 12C1.16421 12 1.5 12.3358 1.5 12.75V15.4393L5.46967 11.4697C5.76256 11.1768 6.23744 11.1768 6.53033 11.4697Z"
    />
  </svg>
);

export const Phone = ({ className }: { className?: string }) => (
  <svg
    width="14"
    height="24"
    viewBox="0 0 14 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M5.5 18.75C5.08579 18.75 4.75 19.0858 4.75 19.5C4.75 19.9142 5.08579 20.25 5.5 20.25H8.5C8.91421 20.25 9.25 19.9142 9.25 19.5C9.25 19.0858 8.91421 18.75 8.5 18.75H5.5Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.625 0.75C1.76104 0.75 0.25 2.26104 0.25 4.125V19.875C0.25 21.739 1.76104 23.25 3.625 23.25H10.375C12.239 23.25 13.75 21.739 13.75 19.875V4.125C13.75 2.26104 12.239 0.75 10.375 0.75H3.625ZM2.5 4.125C2.5 3.50368 3.00368 3 3.625 3H4.75V3.375C4.75 3.99632 5.25368 4.5 5.875 4.5H8.125C8.74632 4.5 9.25 3.99632 9.25 3.375V3H10.375C10.9963 3 11.5 3.50368 11.5 4.125V19.875C11.5 20.4963 10.9963 21 10.375 21H3.625C3.00368 21 2.5 20.4963 2.5 19.875V4.125Z"
    />
  </svg>
);

export const PurpleThickCheck = ({ className }: { className?: string }) => (
  <svg
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M0.666992 4L2.75033 6.08333L7.33366 1.5" stroke="#A586FF" strokeWidth="1.875" />
  </svg>
);

export const Printer = ({ className }: { className?: string }) => (
  <svg
    width="20"
    height="22"
    viewBox="0 0 20 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.875 0.5C4.83947 0.5 4 1.33947 4 2.375V5.36564C3.5736 5.41799 3.1489 5.47583 2.72596 5.53912C1.27191 5.75668 0.25 7.02163 0.25 8.45569V14.75C0.25 16.4069 1.59315 17.75 3.25 17.75H3.51963L3.36461 19.4552C3.26479 20.5533 4.12935 21.5 5.23191 21.5H14.7681C15.8706 21.5 16.7352 20.5533 16.6354 19.4552L16.4804 17.75H16.75C18.4069 17.75 19.75 16.4069 19.75 14.75V8.45569C19.75 7.02163 18.7281 5.75668 17.274 5.53912C16.8511 5.47583 16.4264 5.41799 16 5.36564V2.375C16 1.33947 15.1605 0.5 14.125 0.5H5.875ZM14.5 5.20498V2.375C14.5 2.16789 14.3321 2 14.125 2H5.875C5.66789 2 5.5 2.16789 5.5 2.375V5.20498C6.98198 5.06931 8.48298 5 10 5C11.517 5 13.018 5.06931 14.5 5.20498ZM14.2834 13.4696C14.4607 13.4879 14.5996 13.6298 14.6158 13.8073L15.1415 19.591C15.1615 19.8107 14.9886 20 14.7681 20H5.23191C5.0114 20 4.83849 19.8107 4.85845 19.591L5.38425 13.8073C5.40039 13.6298 5.53926 13.4879 5.71659 13.4696C7.12438 13.3244 8.55338 13.25 10 13.25C11.4466 13.25 12.8756 13.3244 14.2834 13.4696ZM15.25 9.5C15.25 9.08579 15.5858 8.75 16 8.75H16.0075C16.4217 8.75 16.7575 9.08579 16.7575 9.5V9.5075C16.7575 9.92171 16.4217 10.2575 16.0075 10.2575H16C15.5858 10.2575 15.25 9.92171 15.25 9.5075V9.5ZM13 8.75C12.5858 8.75 12.25 9.08579 12.25 9.5V9.5075C12.25 9.92171 12.5858 10.2575 13 10.2575H13.0075C13.4217 10.2575 13.7575 9.92171 13.7575 9.5075V9.5C13.7575 9.08579 13.4217 8.75 13.0075 8.75H13Z"
    />
  </svg>
);

export const OldPhone = ({ className }: { className?: string }) => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.5 3.5C0.5 1.84315 1.84315 0.5 3.5 0.5H4.87163C5.732 0.5 6.48197 1.08556 6.69064 1.92025L7.79644 6.34343C7.97941 7.0753 7.70594 7.84555 7.10242 8.29818L5.8088 9.2684C5.67447 9.36915 5.64527 9.51668 5.683 9.61969C6.81851 12.7195 9.28051 15.1815 12.3803 16.317C12.4833 16.3547 12.6309 16.3255 12.7316 16.1912L13.7018 14.8976C14.1545 14.2941 14.9247 14.0206 15.6566 14.2036L20.0798 15.3094C20.9144 15.518 21.5 16.268 21.5 17.1284V18.5C21.5 20.1569 20.1569 21.5 18.5 21.5H16.25C7.55151 21.5 0.5 14.4485 0.5 5.75V3.5Z"
    />
  </svg>
);
