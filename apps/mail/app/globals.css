@import '@fontsource-variable/geist';
@import '@fontsource-variable/geist-mono';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .horizontal-fade-mask {
    @apply overflow-x-auto;
    position: relative;
  }
  @supports (mask-image: linear-gradient(to right, transparent, black)) or
    (-webkit-mask-image: linear-gradient(to right, transparent, black)) {
    .horizontal-fade-mask::before,
    .horizontal-fade-mask::after {
      content: '';
      position: absolute;
      top: 0;
      height: 100%;
      width: 15%;
      pointer-events: none;
      background: hsl(var(--panel));
      z-index: 1;
    }

    .horizontal-fade-mask::before {
      left: 0;
      -webkit-mask-image: linear-gradient(to right, white, transparent);
      mask-image: linear-gradient(to right, white, transparent);
    }

    .horizontal-fade-mask::after {
      right: 0;
      -webkit-mask-image: linear-gradient(to left, white, transparent);
      mask-image: linear-gradient(to left, white, transparent);
    }
  }
}

@layer base {
  :root {
    --font-geist-sans:
      'Geist Variable', 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui,
      sans-serif;
    --font-geist-mono:
      'Geist Mono Variable', 'Geist Mono', ui-monospace, SFMono-Regular, 'SF Mono', Consolas,
      'Liberation Mono', Menlo, monospace;
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --icon-color: currentColor;
    --panel: 0 0% 100%;
  }

  .dark {
    --background: 1;
    --foreground: 0 0% 98%;
    --card: 240 5.9% 10%;
    --card-foreground: 0 0% 98%;
    --popover: 240 3.4% 8%;
    --popover-foreground: 0 0% 99%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 20%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 3.9% 7%;
    --sidebar-foreground: 240 4.8% 96.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --icon-color: currentColor;
    --panel: 240 3.7% 10.2%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      'Geist Variable',
      'Geist',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      system-ui,
      sans-serif;
    overscroll-behavior: none;
    @apply style-scrollbar;
  }
}

.skeleton {
  * {
    pointer-events: none !important;
  }

  *[class^='text-'] {
    color: transparent;
    @apply bg-foreground/20 animate-pulse select-none rounded-md;
  }

  .skeleton-bg {
    @apply bg-foreground/10;
  }

  .skeleton-div {
    @apply bg-foreground/20 animate-pulse;
  }
}

.ProseMirror {
  outline: none;
}

.cm-editor,
.cm-gutters {
  @apply bg-background outline-none selection:bg-zinc-900 dark:bg-zinc-800 !important;
}

.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
  @apply bg-zinc-200 dark:bg-zinc-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
  @apply bg-transparent !important;
}

.cm-activeLine {
  @apply rounded-r-sm !important;
}

.cm-lineNumbers {
  @apply min-w-7;
}

.cm-foldGutter {
  @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
  @apply rounded-l-sm !important;
}

.suggestion-highlight {
  @apply bg-blue-200 hover:bg-blue-300 dark:bg-blue-500/40 dark:text-blue-50 dark:hover:bg-blue-400/50;
}

.dark [data-hide-on-theme='dark'],
.light [data-hide-on-theme='light'] {
  display: none;
}

@media (prefers-reduced-motion: no-preference) {
  ::view-transition-new(theme-transition) {
    clip-path: inset(0 0 100% 0);
    animation: slide-in-from-top 0.65s forwards cubic-bezier(0.65, 0, 0.35, 1);
  }

  ::view-transition-old(theme-transition) {
    animation: none;
  }

  @keyframes slide-in-from-top {
    to {
      clip-path: inset(0 0 0 0);
    }
  }
}

/* Compose gradient styles */
@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.compose-loading {
  background: #016ffe;
  animation: none;
}

.compose-gradient-animated {
  background: linear-gradient(
    90deg,
    rgba(255, 213, 208, 1),
    rgba(219, 255, 228, 1),
    rgba(226, 214, 255, 1),
    rgba(255, 213, 208, 1)
  );
  background-size: 300% 100%;
  animation: gradient-animation 8s ease infinite;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
  }
}

.compose-gradient-text {
  background: linear-gradient(
    90deg,
    rgba(255, 213, 208, 1) 0%,
    rgba(219, 255, 228, 1) 50%,
    rgba(226, 214, 255, 1) 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.compose-gradient-text-shiny {
  background: linear-gradient(
    90deg,
    rgba(255, 213, 208, 1) 0%,
    rgba(255, 255, 255, 1) 25%,
    rgba(219, 255, 228, 1) 50%,
    rgba(255, 255, 255, 1) 75%,
    rgba(226, 214, 255, 1) 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.3);
}

.compose-gradient-text-shiny::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  transform: translateX(-100%);
  mix-blend-mode: overlay;
  animation: shine-slow 3s ease-in-out infinite;
}

.compose-gradient-inner {
  background-color: hsl(var(--background));
  border-radius: 0.5rem;
  padding: 0.5rem;
}

/* Animation keyframes */
@keyframes shine {
  from {
    background-position: 0% 0;
  }
  to {
    background-position: -200% 0;
  }
}

@keyframes shine-slow {
  from {
    background-position: 0% 0;
  }
  to {
    background-position: -200% 0;
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

.animate-shine {
  animation: shine 3s linear infinite;
}

.animate-shine-slow {
  animation: shine-slow 8s linear infinite;
}

.animate-blink {
  animation: blink 0.8s ease-in-out infinite;
}

.tiptap p {
  margin-bottom: 0.3rem;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Add scrollbar styling */
.style-scrollbar {
  @apply scrollbar scrollbar-w-0 scrollbar-thumb-accent/40 scrollbar-track-transparent hover:scrollbar-thumb-accent scrollbar-thumb-rounded-full;
}
