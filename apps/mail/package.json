{"name": "@zero/mail", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "react-router dev", "build": "react-router build", "start": "wrangler dev --port 3000 --show-interactive-dev-session=false", "types": "wrangler types", "deploy": "wrangler deploy", "lint": "eslint .", "machine-translate": "inlang machine translate --project project.inlang"}, "dependencies": {"@ai-sdk/perplexity": "1.1.9", "@ai-sdk/react": "1.2.12", "@dnd-kit/core": "6.3.1", "@dnd-kit/modifiers": "9.0.0", "@dnd-kit/sortable": "10.0.0", "@dnd-kit/utilities": "3.2.2", "@elevenlabs/react": "0.1.5", "@fontsource-variable/geist": "5.2.6", "@fontsource-variable/geist-mono": "5.2.6", "@hookform/resolvers": "4.1.2", "@intercom/messenger-js-sdk": "0.0.14", "@react-email/components": "^0.0.36", "@react-email/html": "^0.0.11", "@react-email/render": "1.1.0", "@react-router/dev": "^7.6.1", "@sentry/react": "9.19.0", "@tanstack/query-sync-storage-persister": "^5.75.0", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-persist-client": "^5.75.2", "@tiptap/core": "2.23.0", "@tiptap/extension-bold": "2.23.0", "@tiptap/extension-document": "2.23.0", "@tiptap/extension-emoji": "2.23.1", "@tiptap/extension-file-handler": "2.23.0", "@tiptap/extension-image": "2.23.0", "@tiptap/extension-link": "2.23.0", "@tiptap/extension-paragraph": "2.23.0", "@tiptap/extension-placeholder": "2.23.0", "@tiptap/extension-text": "2.23.0", "@tiptap/html": "2.23.0", "@tiptap/pm": "2.23.0", "@tiptap/react": "2.23.0", "@tiptap/starter-kit": "2.23.0", "@trpc/client": "catalog:", "@trpc/server": "catalog:", "@trpc/tanstack-react-query": "catalog:", "@zero/server": "workspace:*", "accept-language-parser": "^1.5.0", "agents": "0.0.93", "ai": "^4.3.9", "babel-plugin-react-compiler": "19.1.0-rc.2", "better-auth": "catalog:", "canvas-confetti": "1.9.3", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.0.0", "color": "5.0.0", "date-fns": "4.1.0", "date-fns-tz": "^3.2.0", "dedent": "^1.5.3", "deepmerge": "4.3.1", "dexie": "4.0.11", "dompurify": "^3.2.5", "email-addresses": "5.0.0", "emblor": "^1.4.8", "eslint-plugin-react-hooks": "6.0.0-rc.1", "idb-keyval": "^6.2.1", "input-otp": "1.4.2", "isbot": "^5.1.28", "jotai": "2.12.1", "jszip": "3.10.1", "lowlight": "3.3.0", "lucide-react": "0.474.0", "lz-string": "1.5.0", "mimetext": "^3.0.27", "motion": "12.4.7", "next-themes": "0.4.4", "novel": "1.0.2", "nuqs": "2.4.0", "oxlint": "1.6.0", "partysocket": "^1.1.4", "pluralize": "^8.0.0", "posthog-js": "1.256.0", "prosemirror-model": "1.25.1", "prosemirror-state": "1.4.3", "prosemirror-view": "1.39.3", "radix-ui": "1.4.1", "react": "catalog:", "react-colorful": "^5.6.1", "react-day-picker": "8.10.1", "react-dom": "catalog:", "react-hook-form": "7.54.2", "react-hotkeys-hook": "^5.0.1", "react-markdown": "^10.1.0", "react-resizable-panels": "2.1.7", "react-router": "^7.6.1", "react-use": "^17.6.0", "react-wrap-balancer": "^1.1.1", "recharts": "^2.15.1", "sonner": "1.7.4", "superjson": "catalog:", "tailwind-merge": "3.0.2", "tailwindcss-animate": "1.0.7", "tiptap-extension-auto-joiner": "0.1.3", "tiptap-extension-global-drag-handle": "0.1.18", "tiptap-markdown": "0.8.10", "vaul": "1.1.2", "virtua": "0.41.2", "vite-plugin-babel": "1.3.1", "vite-plugin-oxlint": "1.4.0", "workers-og": "0.0.25", "zod": "catalog:"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.3.1", "@inlang/cli": "^3.0.0", "@inlang/paraglide-js": "2.1.0", "@tailwindcss/typography": "0.5.16", "@types/accept-language-parser": "^1.5.8", "@types/canvas-confetti": "1.9.0", "@types/node": "22.13.8", "@types/pluralize": "^0.0.33", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/sanitize-html": "2.13.0", "@zero/eslint-config": "workspace:*", "@zero/tsconfig": "workspace:*", "drizzle-kit": "catalog:", "eslint": "9.27.0", "jiti": "2.4.2", "postcss": "8.5.3", "remeda": "2.21.3", "tailwind-scrollbar": "3.1.0", "tailwindcss": "3.4.17", "typescript": "catalog:", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "wrangler": "catalog:"}}