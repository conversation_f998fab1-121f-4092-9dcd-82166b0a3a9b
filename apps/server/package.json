{"name": "@zero/server", "type": "module", "private": true, "scripts": {"dev": "wrangler dev --show-interactive-dev-session=false --experimental-vectorize-bind-to-prod --env local", "deploy": "wrangler deploy", "types": "wrangler types --env local", "lint": "eslint .", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "exports": {"./trpc": "./src/trpc/index.ts", "./auth": "./src/lib/auth.ts", "./auth-providers": "./src/lib/auth-providers.ts", "./schemas": "./src/lib/schemas.ts"}, "dependencies": {"@ai-sdk/anthropic": "1.2.12", "@ai-sdk/google": "^1.2.18", "@ai-sdk/groq": "1.2.9", "@ai-sdk/openai": "^1.3.21", "@ai-sdk/perplexity": "1.1.9", "@ai-sdk/ui-utils": "1.2.11", "@arcadeai/arcadejs": "1.8.1", "@coinbase/cookie-manager": "1.1.8", "@googleapis/gmail": "12.0.0", "@googleapis/people": "3.0.9", "@hono/trpc-server": "^0.3.4", "@microsoft/microsoft-graph-client": "^3.0.7", "@microsoft/microsoft-graph-types": "^2.40.0", "@modelcontextprotocol/sdk": "1.12.0", "@react-email/components": "^0.0.41", "@react-email/render": "1.1.0", "@trpc/client": "catalog:", "@trpc/server": "catalog:", "@tsndr/cloudflare-worker-jwt": "3.2.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "agents": "0.0.93", "ai": "^4.3.13", "autumn-js": "catalog:", "base64-js": "1.5.1", "better-auth": "catalog:", "cheerio": "1.1.0", "date-fns": "^4.1.0", "dedent": "^1.6.0", "drizzle-orm": "catalog:", "effect": "3.16.12", "elevenlabs": "1.59.0", "email-addresses": "^5.0.0", "google-auth-library": "9.15.1", "he": "^1.2.0", "hono": "^4.7.8", "hono-agents": "0.0.83", "hono-party": "^0.0.12", "jose": "6.0.11", "jsonrepair": "^3.12.0", "mime-types": "3.0.1", "mimetext": "^3.0.27", "p-retry": "6.2.1", "partyserver": "^0.0.71", "postgres": "3.4.5", "react": "catalog:", "remeda": "2.21.3", "resend": "^4.5.1", "sanitize-html": "^2.16.0", "string-strip-html": "^13.4.12", "superjson": "catalog:", "twilio": "5.7.0", "uuid": "11.1.0", "wrangler": "catalog:", "zod": "catalog:"}, "devDependencies": {"@types/he": "1.2.3", "@types/node": "^22.9.0", "@types/react": "19.1.6", "@types/sanitize-html": "2.13.0", "@types/uuid": "10.0.0", "@zero/eslint-config": "workspace:*", "@zero/tsconfig": "workspace:*", "drizzle-kit": "catalog:", "eslint": "^9.27.0", "jiti": "2.4.2", "typescript": "catalog:"}}