{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "zero-server",
  "compatibility_date": "2025-05-01",
  "compatibility_flags": ["nodejs_compat"],
  "main": "src/main.ts",
  "env": {
    "local": {
      "ai": {
        "binding": "AI",
      },
      "vectorize": [
        {
          "binding": "VECTORIZE",
          "index_name": "threads-vector-staging",
        },
        {
          "binding": "VECTORIZE_MESSAGE",
          "index_name": "messages-vector-staging",
        },
      ],
      "r2_buckets": [
        {
          "binding": "THREADS_BUCKET",
          "bucket_name": "threads-staging",
        },
      ],
      "durable_objects": {
        "bindings": [
          {
            "name": "ZERO_AGENT",
            "class_name": "ZeroAgent",
          },
          {
            "name": "ZERO_MCP",
            "class_name": "ZeroMCP",
          },
          {
            "name": "ZERO_DB",
            "class_name": "ZeroDB",
          },
          {
            "class_name": "DurableMailbox",
            "name": "DURABLE_MAILBOX",
          },
        ],
      },
      "queues": {
        "producers": [
          {
            "queue": "thread-queue",
            "binding": "thread_queue",
          },
          {
            "queue": "subscribe-queue",
            "binding": "subscribe_queue",
          },
        ],
        "consumers": [
          {
            "queue": "subscribe-queue",
          },
          {
            "queue": "thread-queue",
          },
        ],
      },
      "migrations": [
        {
          "tag": "v1",
          "new_classes": ["DurableMailbox"],
        },
        {
          "tag": "v2",
          "new_sqlite_classes": ["ZeroAgent", "ZeroMCP"],
        },
        {
          "tag": "v3",
          "new_classes": ["ZeroDB"],
        },
      ],

      "observability": {
        "enabled": true,
      },
      "hyperdrive": [
        {
          "binding": "HYPERDRIVE",
          "id": "57834ddb6716440496c8836f6d99bc9a",
          "localConnectionString": "postgresql://postgres:postgres@localhost:5432/zerodotemail",
        },
      ],
      "vars": {
        "NODE_ENV": "development",
        "COOKIE_DOMAIN": "localhost",
        "VITE_PUBLIC_BACKEND_URL": "http://localhost:8787",
        "VITE_PUBLIC_APP_URL": "http://localhost:3000",
        "JWT_SECRET": "secret",
        "ELEVENLABS_API_KEY": "**********",
        "DISABLE_CALLS": "true",
        "VOICE_SECRET": "**********",
        "GOOGLE_S_ACCOUNT": "{}",
        "DROP_AGENT_TABLES": "false",
        "THREAD_SYNC_MAX_COUNT": "40",
        "THREAD_SYNC_LOOP": "false",
        "DISABLE_WORKFLOWS": "true",
        "AUTORAG_ID": "",
      },
      "kv_namespaces": [
        {
          "binding": "gmail_history_id",
          "id": "4e814c70e35d413d99c923029928efae",
        },
        {
          "binding": "gmail_processing_threads",
          "id": "b7db3a98a80f4e16a8b6edc5fa8c7b76",
        },
        {
          "binding": "subscribed_accounts",
          "id": "7e6eadacf19c4c56a9ec3c357adb584a",
        },
        {
          "binding": "connection_labels",
          "id": "4d3a28d3265a4388aae2e9e9b534d019",
        },
        {
          "binding": "prompts_storage",
          "id": "620e710aaea744e59df4788f9ec18ff9",
        },
        {
          "binding": "gmail_sub_age",
          "id": "c55e692bb71d4e5bae23dded092b09d5",
        },
      ],
    },
    "staging": {
      "triggers": {
        "crons": ["0 0 * * *"],
      },
      "ai": {
        "binding": "AI",
      },
      "vectorize": [
        {
          "binding": "VECTORIZE",
          "index_name": "threads-vector-staging",
        },
        {
          "binding": "VECTORIZE_MESSAGE",
          "index_name": "messages-vector-staging",
        },
      ],
      "limits": {
        "cpu_ms": 300000,
      },
      "durable_objects": {
        "bindings": [
          {
            "name": "ZERO_AGENT",
            "class_name": "ZeroAgent",
          },
          {
            "name": "ZERO_MCP",
            "class_name": "ZeroMCP",
          },
          {
            "name": "ZERO_DB",
            "class_name": "ZeroDB",
          },
          {
            "class_name": "DurableMailbox",
            "name": "DURABLE_MAILBOX",
          },
        ],
      },
      "r2_buckets": [
        {
          "binding": "THREADS_BUCKET",
          "bucket_name": "threads-staging",
        },
      ],
      "queues": {
        "producers": [
          {
            "queue": "thread-queue-staging",
            "binding": "thread_queue",
          },
          {
            "queue": "subscribe-queue-staging",
            "binding": "subscribe_queue",
          },
        ],
        "consumers": [
          {
            "queue": "subscribe-queue-staging",
          },
          {
            "queue": "thread-queue-staging",
          },
        ],
      },
      "migrations": [
        {
          "tag": "v1",
          "new_classes": ["DurableMailbox"],
        },
        {
          "tag": "v2",
          "new_sqlite_classes": ["ZeroAgent", "ZeroMCP"],
        },
        {
          "tag": "v3",
          "new_classes": ["ZeroDB"],
        },
        {
          "tag": "v4",
          "new_sqlite_classes": ["ZeroAgent"],
        },
      ],
      "observability": {
        "enabled": true,
      },
      "hyperdrive": [
        {
          "binding": "HYPERDRIVE",
          "id": "57834ddb6716440496c8836f6d99bc9a",
          "localConnectionString": "postgresql://postgres:postgres@localhost:5432/zerodotemail",
        },
      ],
      "vars": {
        "NODE_ENV": "development",
        "COOKIE_DOMAIN": "0.email",
        "VITE_PUBLIC_BACKEND_URL": "https://sapi.0.email",
        "VITE_PUBLIC_APP_URL": "https://staging.0.email",
        "DISABLE_CALLS": "",
        "DROP_AGENT_TABLES": "false",
        "THREAD_SYNC_MAX_COUNT": "40",
        "THREAD_SYNC_LOOP": "true",
        "DISABLE_WORKFLOWS": "true",
      },
      "kv_namespaces": [
        {
          "binding": "gmail_history_id",
          "id": "4e814c70e35d413d99c923029928efae",
        },
        {
          "binding": "gmail_processing_threads",
          "id": "b7db3a98a80f4e16a8b6edc5fa8c7b76",
        },
        {
          "binding": "subscribed_accounts",
          "id": "7e6eadacf19c4c56a9ec3c357adb584a",
        },
        {
          "binding": "connection_labels",
          "id": "4d3a28d3265a4388aae2e9e9b534d019",
        },
        {
          "binding": "prompts_storage",
          "id": "620e710aaea744e59df4788f9ec18ff9",
        },
        {
          "binding": "gmail_sub_age",
          "id": "c55e692bb71d4e5bae23dded092b09d5",
        },
      ],
    },
    "production": {
      "triggers": {
        "crons": ["0 0 * * *"],
      },
      "r2_buckets": [
        {
          "binding": "THREADS_BUCKET",
          "bucket_name": "threads",
        },
      ],
      "ai": {
        "binding": "AI",
      },
      "vectorize": [
        {
          "binding": "VECTORIZE",
          "index_name": "threads-vector",
        },
        {
          "binding": "VECTORIZE_MESSAGE",
          "index_name": "messages-vector",
        },
      ],
      "observability": {
        "enabled": true,
      },
      "hyperdrive": [
        {
          "binding": "HYPERDRIVE",
          "id": "b1be316b45fb439a9e54b74ecc20aa21",
          "localConnectionString": "postgresql://postgres:postgres@localhost:5432/zerodotemail",
        },
      ],
      "durable_objects": {
        "bindings": [
          {
            "name": "ZERO_AGENT",
            "class_name": "ZeroAgent",
          },
          {
            "name": "ZERO_MCP",
            "class_name": "ZeroMCP",
          },
          {
            "name": "ZERO_DB",
            "class_name": "ZeroDB",
          },
          {
            "class_name": "DurableMailbox",
            "name": "DURABLE_MAILBOX",
          },
        ],
      },
      "queues": {
        "producers": [
          {
            "queue": "thread-queue-prod",
            "binding": "thread_queue",
          },
          {
            "queue": "subscribe-queue-prod",
            "binding": "subscribe_queue",
          },
        ],
        "consumers": [
          {
            "queue": "subscribe-queue-prod",
          },
          {
            "queue": "thread-queue-prod",
          },
        ],
      },
      "migrations": [
        {
          "tag": "v1",
          "new_classes": ["DurableMailbox"],
        },
        {
          "tag": "v2",
          "new_sqlite_classes": ["ZeroAgent", "ZeroMCP"],
        },
        {
          "tag": "v3",
          "new_classes": ["ZeroDB"],
        },
        {
          "tag": "v4",
          "new_sqlite_classes": ["ZeroAgent"],
        },
      ],
      "vars": {
        "NODE_ENV": "production",
        "COOKIE_DOMAIN": "0.email",
        "VITE_PUBLIC_BACKEND_URL": "https://api.0.email",
        "VITE_PUBLIC_APP_URL": "https://0.email",
        "DISABLE_CALLS": "true",
        "DROP_AGENT_TABLES": "false",
        "THREAD_SYNC_MAX_COUNT": "40",
        "THREAD_SYNC_LOOP": "true",
        "DISABLE_WORKFLOWS": "true",
      },
      "kv_namespaces": [
        {
          "binding": "gmail_history_id",
          "id": "10005d74e84f4f18a17c9618d9e9cecf",
        },
        {
          "binding": "gmail_processing_threads",
          "id": "3348ff0976284269a8d8a5e6e4c04c56",
        },
        {
          "binding": "subscribed_accounts",
          "id": "5902b3b948ff4c4ba1aedbbbbe25503d",
        },
        {
          "binding": "connection_labels",
          "id": "9a13290a55ad4f62824c67005dd66f6f",
        },
        {
          "binding": "prompts_storage",
          "id": "2a4ebda553f3456085cfcf92cc0f570f",
        },
        {
          "binding": "gmail_sub_age",
          "id": "0591e91fffcc4675aaf00f909bee77d2",
        },
      ],
    },
  },
}
