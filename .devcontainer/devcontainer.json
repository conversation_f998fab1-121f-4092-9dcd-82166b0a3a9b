// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-docker-compose
{
  "name": "0.email Dev Container",
  "build": {
    "dockerfile": "Dockerfile",
    "context": "."
  },
  // The optional 'workspaceFolder' property is the path VS Code should open by default when
  // connected. This is typically a file mount in .devcontainer/docker-compose.yml
  "workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}",

  // Features to add to the dev container
  "features": {
    "ghcr.io/devcontainers/features/docker-in-docker:2": {}
  },

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  "forwardPorts": [],

  // Configure tool-specific properties.
  "customizations": {
    "vscode": {
      "extensions": [
        "ms-azuretools.vscode-docker",
        "eamodio.gitlens",
        "streetsidesoftware.code-spell-checker"
      ],
      "settings": {}
    }
  },

  // Use 'postCreateCommand' to run commands after the container is created.
  "postCreateCommand": "apt update && apt install -y zsh && zsh"
}
