# Simplified Supabase Setup Guide

## ✅ What You Have Already Done:
- ✅ Added Supabase URL and anon key to `.env.local`
- ✅ Removed unnecessary dependencies (autumn-js, resend)

## 🔧 What You Need to Complete:

### 1. **Set Up Supabase Database**
```bash
# Install Supabase CLI (if not already installed)
npm install -g supabase

# Initialize Supabase in your project
supabase init

# Link to your existing project
supabase link --project-ref esscvnoymacweuquyvny

# Push the database schema
supabase db push
```

### 2. **Configure OAuth in Supabase Dashboard**
Go to: https://supabase.com/dashboard/project/esscvnoymacweuquyvny/auth/providers

**Enable Google OAuth:**
- Client ID: (use your existing `GOOGLE_CLIENT_ID`)
- Client Secret: (use your existing `GOOGLE_CLIENT_SECRET`)
- Redirect URL: `https://esscvnoymacweuquyvny.supabase.co/auth/v1/callback`

### 3. **Set AI Keys (Optional)**
Only if you want AI features:
```bash
# Set OpenAI key for AI chat and email processing
supabase secrets set OPENAI_API_KEY=your-openai-key

# Optional: Set Perplexity for web search in AI responses
# supabase secrets set PERPLEXITY_API_KEY=your-perplexity-key
```

### 4. **Deploy Edge Functions (Optional)**
Only if you want AI features:
```bash
# Deploy AI chat function
supabase functions deploy ai-chat

# Deploy email processor function
supabase functions deploy email-processor
```

## 🚫 **Services You DON'T Need:**

### ❌ **Resend** 
- **Why removed**: You're using Gmail API to read/send emails directly
- **Resend was for**: Sending transactional emails FROM the app (welcome emails, etc.)
- **You don't need it because**: Gmail API handles all email operations

### ❌ **Twilio**
- **Why removed**: Was for SMS/phone verification
- **You don't need it because**: Supabase has built-in phone auth if needed

### ❌ **Autumn**
- **Why removed**: Custom service specific to your old backend
- **You don't need it because**: Supabase replaces this functionality

### ❓ **Perplexity** (Optional)
- **What it does**: AI search engine for current web information
- **Used for**: Making AI responses include up-to-date web search results
- **Keep it if**: You want AI to search the web for current info
- **Skip it if**: Basic AI chat without web search is sufficient

## 🎯 **Your Minimal Setup:**

For basic functionality, you only need:
```bash
# .env.local (you already have this!)
VITE_PUBLIC_SUPABASE_URL=https://esscvnoymacweuquyvny.supabase.co
VITE_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Optional: For AI features
OPENAI_API_KEY=your-openai-key
```

## 🚀 **Next Steps:**

1. **Run the database migration** (Step 1 above)
2. **Configure Google OAuth** (Step 2 above)
3. **Test basic functionality** (user login, email connections)
4. **Add AI features later** if needed (Steps 3-4 above)

## 🔍 **What Each Service Actually Does:**

| Service | Purpose | Status |
|---------|---------|---------|
| **Gmail API** | Read user's emails | ✅ Keep (core feature) |
| **Supabase** | Database, auth, real-time | ✅ Keep (replacement for backend) |
| **OpenAI** | AI chat, email summaries | ❓ Optional (for AI features) |
| **Perplexity** | Web search in AI responses | ❓ Optional (for current info) |
| **Resend** | Send emails FROM app | ❌ Removed (Gmail API handles this) |
| **Twilio** | SMS verification | ❌ Removed (Supabase has phone auth) |
| **Autumn** | Custom backend service | ❌ Removed (replaced by Supabase) |

Your setup is now much cleaner and focused on the core email management functionality!
