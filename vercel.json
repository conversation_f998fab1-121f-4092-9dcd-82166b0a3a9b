{"buildCommand": "pnpm build:frontend", "outputDirectory": "apps/mail/build/client", "installCommand": "pnpm install", "framework": null, "env": {"VITE_PUBLIC_SUPABASE_URL": "@supabase_url", "VITE_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "VITE_PUBLIC_APP_URL": "@app_url"}, "build": {"env": {"VITE_PUBLIC_SUPABASE_URL": "@supabase_url", "VITE_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "VITE_PUBLIC_APP_URL": "@app_url"}}, "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}