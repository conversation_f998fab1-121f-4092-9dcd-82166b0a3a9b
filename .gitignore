# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
.next
out

# production
build
dist

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
bun-debug.log*
# env files (can opt-in for committing if needed)
.env
.dev.vars

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# ide
.idea
.vscode
.turbo
i18n.cache
apps/mail/scripts.ts

.wrangler
worker-configuration.d.ts

.dev.vars.*
.react-router

# devcontainer
.pnpm-store
tsx-0/

tools.json