VITE_PUBLIC_APP_URL=http://localhost:3000
VITE_PUBLIC_BACKEND_URL=http://localhost:8787

DATABASE_URL="postgresql://postgres:postgres@localhost:5432/zerodotemail"

# Change this to a random string, use `openssl rand -hex 32` to generate a 32 character string
BETTER_AUTH_SECRET=my-better-auth-secret
BETTER_AUTH_URL=http://localhost:3000

COOKIE_DOMAIN="localhost"

# Change to your project's client ID and secret, these work with localhost:8787
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Upstash/Local Redis Instance
REDIS_URL="http://localhost:8079"
REDIS_TOKEN="upstash-local-token"

# Resend API Key
RESEND_API_KEY=

# OpenAI API Key
OPENAI_API_KEY=
PERPLEXITY_API_KEY=

# OpenAI Model names (gpt-4o, gpt-4o-mini etc)
OPENAI_MODEL=
OPENAI_MINI_MODEL=

#AI PROMPT
AI_SYSTEM_PROMPT=""

NODE_ENV="development"

AUTUMN_SECRET_KEY=

TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=