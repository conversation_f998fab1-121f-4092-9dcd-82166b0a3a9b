diff --git a/dist/index.js b/dist/index.js
index 8f65954b0ba6dc83bcc756c8cbeff7ceda72b87b..b3780eb39a6424daef6eaa28a5a344073de53137 100644
--- a/dist/index.js
+++ b/dist/index.js
@@ -1,2 +1,2 @@
-import {EditorProvider,useCurrentEditor,isNodeSelection,BubbleMenu,ReactNodeViewRenderer,NodeViewWrapper,ReactRenderer}from'@tiptap/react';export{useCurrentEditor as useEditor}from'@tiptap/react';import {Extension,Node,mergeAttributes,nodePasteRule,Mark,markInputRule,markPasteRule,InputRule}from'@tiptap/core';export{InputRule}from'@tiptap/core';import*as $e from'jotai';import {createStore,atom,useAtom,useAtomValue,Provider,useSet<PERSON>tom}from'jotai';import {createContext,forwardRef,useRef,useEffect,useMemo}from'react';import pt from'tunnel-rat';import {Command,CommandItem,CommandEmpty}from'cmdk';import {jsx,jsxs}from'react/jsx-runtime';import {Slot}from'@radix-ui/react-slot';export{Color}from'@tiptap/extension-color';import fe from'@tiptap/extension-highlight';import ge from'@tiptap/extension-horizontal-rule';import Jt from'@tiptap/extension-image';export{default as TiptapImage}from'@tiptap/extension-image';export{default as TiptapLink}from'@tiptap/extension-link';import Ee from'@tiptap/extension-placeholder';export{TaskItem}from'@tiptap/extension-task-item';export{TaskList}from'@tiptap/extension-task-list';export{default as TextStyle}from'@tiptap/extension-text-style';export{default as TiptapUnderline}from'@tiptap/extension-underline';export{default as StarterKit}from'@tiptap/starter-kit';import Lt from'react-moveable';import {PluginKey,Plugin}from'@tiptap/pm/state';import Dt from'katex';import {Tweet}from'react-tweet';export{default as CharacterCount}from'@tiptap/extension-character-count';export{default as CodeBlockLowlight}from'@tiptap/extension-code-block-lowlight';export{default as Youtube}from'@tiptap/extension-youtube';export{default as GlobalDragHandle}from'tiptap-extension-global-drag-handle';import se from'@tiptap/suggestion';import de from'tippy.js';import {DecorationSet,Decoration}from'@tiptap/pm/view';import {Fragment}from'@tiptap/pm/model';var M=Object.defineProperty;var Z=Object.getOwnPropertyDescriptor;var j=Object.getOwnPropertyNames;var tt=Object.prototype.hasOwnProperty;var et=(t,e)=>{for(var o in e)M(t,o,{get:e[o],enumerable:true});},H=(t,e,o,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of j(e))!tt.call(t,n)&&n!==o&&M(t,n,{get:()=>e[n],enumerable:!(r=Z(e,n))||r.enumerable});return t},y=(t,e,o)=>(H(t,e,"default"),o);var l={};et(l,{novelStore:()=>p});y(l,$e);var p=createStore();var E=atom(""),u=atom(null);var b=createContext({}),k=({query:t,range:e})=>{let o=useSetAtom(E,{store:p}),r=useSetAtom(u,{store:p});return useEffect(()=>{o(t);},[t,o]),useEffect(()=>{r(e);},[e,r]),useEffect(()=>{let n=["ArrowUp","ArrowDown","Enter"],i=a=>{if(n.includes(a.key)){a.preventDefault();let s=document.querySelector("#slash-command");return s&&s.dispatchEvent(new KeyboardEvent("keydown",{key:a.key,cancelable:true,bubbles:true})),false}};return document.addEventListener("keydown",i),()=>{document.removeEventListener("keydown",i);}},[]),jsx(b.Consumer,{children:n=>jsx(n.Out,{})})},T=forwardRef(({children:t,className:e,...o},r)=>{let[n,i]=useAtom(E);return jsx(b.Consumer,{children:a=>jsx(a.In,{children:jsxs(Command,{ref:r,onKeyDown:s=>{s.stopPropagation();},id:"slash-command",className:e,...o,children:[jsx(Command.Input,{value:n,onValueChange:i,style:{display:"none"}}),t]})})})}),O=Command.List;T.displayName="EditorCommand";var U=({children:t})=>{let e=useRef(pt()).current;return jsx(Provider,{store:p,children:jsx(b.Provider,{value:e,children:t})})},I=forwardRef(({className:t,children:e,initialContent:o,...r},n)=>jsx("div",{ref:n,className:t,children:jsx(EditorProvider,{...r,content:o,children:e})}));I.displayName="EditorContent";var P=forwardRef(({children:t,tippyOptions:e,...o},r)=>{let{editor:n}=useCurrentEditor(),i=useRef(null);useEffect(()=>{!i.current||!e?.placement||(i.current.setProps({placement:e.placement}),i.current.popperInstance?.update());},[e?.placement]);let a=useMemo(()=>({shouldShow:({editor:d,state:m})=>{let{selection:h}=m,{empty:c}=h;return !(!d.isEditable||d.isActive("image")||c||isNodeSelection(h))},tippyOptions:{onCreate:d=>{i.current=d,i.current.popper.firstChild?.addEventListener("blur",m=>{m.preventDefault(),m.stopImmediatePropagation();});},moveTransition:"transform 0.15s ease-out",...e},editor:n,...o}),[o,e]);return n?jsx("div",{ref:r,children:jsx(BubbleMenu,{...a,children:t})}):null});P.displayName="EditorBubble";var w=forwardRef(({children:t,asChild:e,onSelect:o,...r},n)=>{let{editor:i}=useCurrentEditor(),a=e?Slot:"div";return i?jsx(a,{ref:n,...r,onClick:()=>o?.(i),children:t}):null});w.displayName="EditorBubbleItem";var S=forwardRef(({children:t,onCommand:e,...o},r)=>{let{editor:n}=useCurrentEditor(),i=useAtomValue(u);return !n||!i?null:jsx(CommandItem,{ref:r,...o,onSelect:()=>e({editor:n,range:i}),children:t})});S.displayName="EditorCommandItem";var B=CommandEmpty;var Mt=Extension.create({name:"CustomKeymap",addCommands(){return {selectTextWithinNodeBoundaries:()=>({editor:t,commands:e})=>{let{state:o}=t,{tr:r}=o,n=r.selection.$from.start(),i=r.selection.$to.end();return e.setTextSelection({from:n,to:i})}}},addKeyboardShortcuts(){return {"Mod-a":({editor:t})=>{let{state:e}=t,{tr:o}=e,r=o.selection.from,n=o.selection.to,i=o.selection.$from.start(),a=o.selection.$to.end();return r>i||n<a?(t.chain().selectTextWithinNodeBoundaries().run(),true):false}}}}),z=Mt;var K=()=>{let{editor:t}=useCurrentEditor();if(!t?.isActive("image"))return null;let e=()=>{let o=document.querySelector(".ProseMirror-selectednode");if(o){let r=t.state.selection,n=t.commands.setImage;n({src:o.src,width:Number(o.style.width.replace("px","")),height:Number(o.style.height.replace("px",""))}),t.commands.setNodeSelection(r.from);}};return jsx(Lt,{target:document.querySelector(".ProseMirror-selectednode"),container:null,origin:false,edge:false,throttleDrag:0,keepRatio:true,resizable:true,throttleResize:0,onResize:({target:o,width:r,height:n,delta:i})=>{i[0]&&(o.style.width=`${r}px`),i[1]&&(o.style.height=`${n}px`);},onResizeEnd:()=>{e();},scalable:true,throttleScale:0,renderDirections:["w","e"],onScale:({target:o,transform:r})=>{o.style.transform=r;}})};var F=Node.create({name:"math",inline:true,group:"inline",atom:true,selectable:true,marks:"",addAttributes(){return {latex:""}},addOptions(){return {shouldRender:(t,e)=>{let o=t.doc.resolve(e);return o.parent.isTextblock?o.parent.type.name!=="codeBlock":false},katexOptions:{throwOnError:false},HTMLAttributes:{}}},addCommands(){return {setLatex:({latex:t})=>({chain:e,state:o})=>{if(!t)return  false;let{from:r,to:n,$anchor:i}=o.selection;return this.options.shouldRender(o,i.pos)?e().insertContentAt({from:r,to:n},{type:"math",attrs:{latex:t}}).setTextSelection({from:r,to:r+1}).run():false},unsetLatex:()=>({editor:t,state:e,chain:o})=>{let r=t.getAttributes(this.name).latex;if(typeof r!="string")return  false;let{from:n,to:i}=e.selection;return o().command(({tr:a})=>(a.insertText(r,n,i),true)).setTextSelection({from:n,to:n+r.length}).run()}}},parseHTML(){return [{tag:`span[data-type="${this.name}"]`}]},renderHTML({node:t,HTMLAttributes:e}){let o=t.attrs.latex??"";return ["span",mergeAttributes(e,{"data-type":this.name}),o]},renderText({node:t}){return t.attrs.latex??""},addNodeView(){return ({node:t,HTMLAttributes:e,getPos:o,editor:r})=>{let n=document.createElement("span"),i=t.attrs.latex??"";return Object.entries(this.options.HTMLAttributes).forEach(([a,s])=>{n.setAttribute(a,s);}),Object.entries(e).forEach(([a,s])=>{n.setAttribute(a,s);}),n.addEventListener("click",a=>{if(r.isEditable&&typeof o=="function"){let s=o(),d=t.nodeSize;r.commands.setTextSelection({from:s,to:s+d});}}),n.contentEditable="false",n.innerHTML=Dt.renderToString(i,this.options.katexOptions),{dom:n}}}});var Wt=/(https?:\/\/)?(www\.)?x\.com\/([a-zA-Z0-9_]{1,15})(\/status\/(\d+))?(\/\S*)?/g,_t=/^https?:\/\/(www\.)?x\.com\/([a-zA-Z0-9_]{1,15})(\/status\/(\d+))?(\/\S*)?$/,qt=t=>t.match(_t),Gt=({node:t})=>{let o=t?.attrs?.src?.split("/").pop();return o?jsx(NodeViewWrapper,{children:jsx("div",{"data-twitter":"",children:jsx(Tweet,{id:o})})}):null},$=Node.create({name:"twitter",addOptions(){return {addPasteHandler:true,HTMLAttributes:{},inline:false,origin:""}},addNodeView(){return ReactNodeViewRenderer(Gt,{attrs:this.options.HTMLAttributes})},inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:true,addAttributes(){return {src:{default:null}}},parseHTML(){return [{tag:"div[data-twitter]"}]},addCommands(){return {setTweet:t=>({commands:e})=>qt(t.src)?e.insertContent({type:this.name,attrs:t}):false}},addPasteRules(){return this.options.addPasteHandler?[nodePasteRule({find:Wt,type:this.type,getAttributes:t=>({src:t.input})})]:[]},renderHTML({HTMLAttributes:t}){return ["div",mergeAttributes({"data-twitter":""},t)]}});var Xt=Jt.extend({name:"image",addAttributes(){return {...this.parent?.(),width:{default:null},height:{default:null}}}}),V=Xt;var te=/(?:^|\s)((?:==)((?:[^~=]+))(?:==))$/,ee=/(?:^|\s)((?:==)((?:[^~=]+))(?:==))/g,oe=Mark.create({name:"ai-highlight",addOptions(){return {HTMLAttributes:{}}},addAttributes(){return {color:{default:null,parseHTML:t=>t.getAttribute("data-color")||t.style.backgroundColor,renderHTML:t=>t.color?{"data-color":t.color,style:`background-color: ${t.color}; color: inherit`}:{}}}},parseHTML(){return [{tag:"mark"}]},renderHTML({HTMLAttributes:t}){return ["mark",mergeAttributes(this.options.HTMLAttributes,t),0]},addCommands(){return {setAIHighlight:t=>({commands:e})=>e.setMark(this.name,t),toggleAIHighlight:t=>({commands:e})=>e.toggleMark(this.name,t),unsetAIHighlight:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return {"Mod-Shift-h":()=>this.editor.commands.toggleAIHighlight()}},addInputRules(){return [markInputRule({find:te,type:this.type})]},addPasteRules(){return [markPasteRule({find:ee,type:this.type})]}}),re=t=>{let e=t.state.tr;e.removeMark(0,t.state.doc.nodeSize-2,t.state.schema.marks["ai-highlight"]),t.view.dispatch(e);},ne=(t,e)=>{t.chain().setAIHighlight({color:e??"#c1ecf970"}).run();};var me=Extension.create({name:"slash-command",addOptions(){return {suggestion:{char:"/",command:({editor:t,range:e,props:o})=>{o.command({editor:t,range:e});}}}},addProseMirrorPlugins(){return [se({editor:this.editor,...this.options.suggestion})]}}),le=t=>{let e=null,o=null;return {onStart:r=>{e=new ReactRenderer(k,{props:r,editor:r.editor});let{selection:n}=r.editor.state;if(n.$from.node(n.$from.depth).type.name==="codeBlock")return  false;o=de("body",{getReferenceClientRect:r.clientRect,appendTo:()=>t?t.current:document.body,content:e.element,showOnCreate:true,interactive:true,trigger:"manual",placement:"bottom-start"});},onUpdate:r=>{e?.updateProps(r),o?.[0]?.setProps({getReferenceClientRect:r.clientRect});},onKeyDown:r=>r.event.key==="Escape"?(o?.[0]?.hide(),true):e?.ref?.onKeyDown(r),onExit:()=>{o?.[0]?.destroy(),e?.destroy();}}},pe=t=>t,ce=t=>{if(["ArrowUp","ArrowDown","Enter"].includes(t.key)&&document.querySelector("#slash-command"))return  true};var Ae=Ee.configure({placeholder:({node:t})=>t.type.name==="heading"?`Heading ${t.attrs.level}`:"Press '/' for commands",includeChildren:true}),ve=fe.configure({multicolor:true}),He=ge.extend({addInputRules(){return [new InputRule({find:/^(?:---|—-|___\s|\*\*\*\s)$/u,handler:({state:t,range:e})=>{let o={},{tr:r}=t,n=e.from,i=e.to;r.insert(n-1,this.type.create(o)).delete(r.mapping.map(n),r.mapping.map(i));}})]}});var g=new PluginKey("upload-image"),_=({imageClass:t})=>new Plugin({key:g,state:{init(){return DecorationSet.empty},apply(e,o){o=o.map(e.mapping,e.doc);let r=e.getMeta(this);if(r?.add){let{id:n,pos:i,src:a}=r.add,s=document.createElement("div");s.setAttribute("class","img-placeholder");let d=document.createElement("img");d.setAttribute("class",t),d.src=a,s.appendChild(d);let m=Decoration.widget(i+1,s,{id:n});o=o.add(e.doc,[m]);}else r?.remove&&(o=o.remove(o.find(undefined,undefined,n=>n.id==r.remove.id)));return o}},props:{decorations(e){return this.getState(e)}}});function Oe(t,e){let r=g.getState(t).find(undefined,undefined,n=>n.id==e);return r.length?r[0]?.from:null}var q=({validateFn:t,onUpload:e})=>(o,r,n)=>{if(!t?.(o))return;let a={},s=r.state.tr;s.selection.empty||s.deleteSelection();let d=new FileReader;d.readAsDataURL(o),d.onload=()=>{s.setMeta(g,{add:{id:a,pos:n,src:d.result}}),r.dispatch(s);},e(o).then(m=>{let{schema:h}=r.state,c=Oe(r.state,a);if(c==null)return;let Y=typeof m=="object"?d.result:m,v=h.nodes.image?.create({src:Y});if(!v)return;let Q=r.state.tr.replaceWith(c,c,v).setMeta(g,{remove:{id:a}});r.dispatch(Q);},()=>{let m=r.state.tr.delete(n,n).setMeta(g,{remove:{id:a}});r.dispatch(m);});},G=(t,e,o)=>{if(e.clipboardData?.files.length){e.preventDefault();let[r]=Array.from(e.clipboardData.files),n=t.state.selection.from;return r&&o(r,t,n),true}return  false},J=(t,e,o,r)=>{if(!o&&e.dataTransfer?.files.length){e.preventDefault();let[n]=Array.from(e.dataTransfer.files),i=t.posAtCoords({left:e.clientX,top:e.clientY});return n&&r(n,t,i?.pos??-1),true}return  false};function X(t){try{return new URL(t),!0}catch{return  false}}function De(t){if(X(t))return t;try{if(t.includes(".")&&!t.includes(" "))return new URL(`https://${t}`).toString()}catch{return null}}var Be=(t,e)=>{let o=[];t.state.doc.forEach((i,a)=>a>=e?false:(o.push(i),true));let r=Fragment.fromArray(o),n=t.state.doc.copy(r);return t.storage.markdown.serializer.serialize(n)},ze=t=>{let e=t.state.doc.content,o=t.state.doc.copy(e);return t.storage.markdown.serializer.serialize(o)};
-export{oe as AIHighlight,me as Command,z as CustomKeymap,P as EditorBubble,w as EditorBubbleItem,T as EditorCommand,B as EditorCommandEmpty,S as EditorCommandItem,O as EditorCommandList,I as EditorContent,U as EditorRoot,ve as HighlightExtension,He as HorizontalRule,K as ImageResizer,F as Mathematics,Ae as Placeholder,$ as Twitter,V as UpdatedImage,_ as UploadImagesPlugin,ne as addAIHighlight,q as createImageUpload,pe as createSuggestionItems,ze as getAllContent,Be as getPrevText,De as getUrlFromString,ce as handleCommandNavigation,J as handleImageDrop,G as handleImagePaste,X as isValidUrl,E as queryAtom,u as rangeAtom,re as removeAIHighlight,le as renderItems};
\ No newline at end of file
+import {EditorProvider,useCurrentEditor,isNodeSelection,BubbleMenu,ReactNodeViewRenderer,NodeViewWrapper,ReactRenderer}from'@tiptap/react';export{useCurrentEditor as useEditor}from'@tiptap/react';import {Extension,Node,mergeAttributes,nodePasteRule,Mark,markInputRule,markPasteRule,InputRule}from'@tiptap/core';export{InputRule}from'@tiptap/core';import*as $e from'jotai';import {createStore,atom,useAtom,useAtomValue,Provider,useSetAtom}from'jotai';import {createContext,forwardRef,useRef,useEffect,useMemo}from'react';import pt from'tunnel-rat';import {Command,CommandItem,CommandEmpty}from'cmdk';import {jsx,jsxs}from'react/jsx-runtime';import {Slot}from'@radix-ui/react-slot';export{Color}from'@tiptap/extension-color';import fe from'@tiptap/extension-highlight';import ge from'@tiptap/extension-horizontal-rule';import Jt from'@tiptap/extension-image';export{default as TiptapImage}from'@tiptap/extension-image';export{default as TiptapLink}from'@tiptap/extension-link';import Ee from'@tiptap/extension-placeholder';export{TaskItem}from'@tiptap/extension-task-item';export{TaskList}from'@tiptap/extension-task-list';export{default as TextStyle}from'@tiptap/extension-text-style';export{default as TiptapUnderline}from'@tiptap/extension-underline';export{default as StarterKit}from'@tiptap/starter-kit';import Lt from'react-moveable';import {PluginKey,Plugin}from'@tiptap/pm/state';import Dt from'katex';export{default as CharacterCount}from'@tiptap/extension-character-count';export{default as CodeBlockLowlight}from'@tiptap/extension-code-block-lowlight';export{default as Youtube}from'@tiptap/extension-youtube';export{default as GlobalDragHandle}from'tiptap-extension-global-drag-handle';import se from'@tiptap/suggestion';import de from'tippy.js';import {DecorationSet,Decoration}from'@tiptap/pm/view';import {Fragment}from'@tiptap/pm/model';var M=Object.defineProperty;var Z=Object.getOwnPropertyDescriptor;var j=Object.getOwnPropertyNames;var tt=Object.prototype.hasOwnProperty;var et=(t,e)=>{for(var o in e)M(t,o,{get:e[o],enumerable:true});},H=(t,e,o,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of j(e))!tt.call(t,n)&&n!==o&&M(t,n,{get:()=>e[n],enumerable:!(r=Z(e,n))||r.enumerable});return t},y=(t,e,o)=>(H(t,e,"default"),o);var l={};et(l,{novelStore:()=>p});y(l,$e);var p=createStore();var E=atom(""),u=atom(null);var b=createContext({}),k=({query:t,range:e})=>{let o=useSetAtom(E,{store:p}),r=useSetAtom(u,{store:p});return useEffect(()=>{o(t);},[t,o]),useEffect(()=>{r(e);},[e,r]),useEffect(()=>{let n=["ArrowUp","ArrowDown","Enter"],i=a=>{if(n.includes(a.key)){a.preventDefault();let s=document.querySelector("#slash-command");return s&&s.dispatchEvent(new KeyboardEvent("keydown",{key:a.key,cancelable:true,bubbles:true})),false}};return document.addEventListener("keydown",i),()=>{document.removeEventListener("keydown",i);}},[]),jsx(b.Consumer,{children:n=>jsx(n.Out,{})})},T=forwardRef(({children:t,className:e,...o},r)=>{let[n,i]=useAtom(E);return jsx(b.Consumer,{children:a=>jsx(a.In,{children:jsxs(Command,{ref:r,onKeyDown:s=>{s.stopPropagation();},id:"slash-command",className:e,...o,children:[jsx(Command.Input,{value:n,onValueChange:i,style:{display:"none"}}),t]})})})}),O=Command.List;T.displayName="EditorCommand";var U=({children:t})=>{let e=useRef(pt()).current;return jsx(Provider,{store:p,children:jsx(b.Provider,{value:e,children:t})})},I=forwardRef(({className:t,children:e,initialContent:o,...r},n)=>jsx("div",{ref:n,className:t,children:jsx(EditorProvider,{...r,content:o,children:e})}));I.displayName="EditorContent";var P=forwardRef(({children:t,tippyOptions:e,...o},r)=>{let{editor:n}=useCurrentEditor(),i=useRef(null);useEffect(()=>{!i.current||!e?.placement||(i.current.setProps({placement:e.placement}),i.current.popperInstance?.update());},[e?.placement]);let a=useMemo(()=>({shouldShow:({editor:d,state:m})=>{let{selection:h}=m,{empty:c}=h;return !(!d.isEditable||d.isActive("image")||c||isNodeSelection(h))},tippyOptions:{onCreate:d=>{i.current=d,i.current.popper.firstChild?.addEventListener("blur",m=>{m.preventDefault(),m.stopImmediatePropagation();});},moveTransition:"transform 0.15s ease-out",...e},editor:n,...o}),[o,e]);return n?jsx("div",{ref:r,children:jsx(BubbleMenu,{...a,children:t})}):null});P.displayName="EditorBubble";var w=forwardRef(({children:t,asChild:e,onSelect:o,...r},n)=>{let{editor:i}=useCurrentEditor(),a=e?Slot:"div";return i?jsx(a,{ref:n,...r,onClick:()=>o?.(i),children:t}):null});w.displayName="EditorBubbleItem";var S=forwardRef(({children:t,onCommand:e,...o},r)=>{let{editor:n}=useCurrentEditor(),i=useAtomValue(u);return !n||!i?null:jsx(CommandItem,{ref:r,...o,onSelect:()=>e({editor:n,range:i}),children:t})});S.displayName="EditorCommandItem";var B=CommandEmpty;var Mt=Extension.create({name:"CustomKeymap",addCommands(){return {selectTextWithinNodeBoundaries:()=>({editor:t,commands:e})=>{let{state:o}=t,{tr:r}=o,n=r.selection.$from.start(),i=r.selection.$to.end();return e.setTextSelection({from:n,to:i})}}},addKeyboardShortcuts(){return {"Mod-a":({editor:t})=>{let{state:e}=t,{tr:o}=e,r=o.selection.from,n=o.selection.to,i=o.selection.$from.start(),a=o.selection.$to.end();return r>i||n<a?(t.chain().selectTextWithinNodeBoundaries().run(),true):false}}}}),z=Mt;var K=()=>{let{editor:t}=useCurrentEditor();if(!t?.isActive("image"))return null;let e=()=>{let o=document.querySelector(".ProseMirror-selectednode");if(o){let r=t.state.selection,n=t.commands.setImage;n({src:o.src,width:Number(o.style.width.replace("px","")),height:Number(o.style.height.replace("px",""))}),t.commands.setNodeSelection(r.from);}};return jsx(Lt,{target:document.querySelector(".ProseMirror-selectednode"),container:null,origin:false,edge:false,throttleDrag:0,keepRatio:true,resizable:true,throttleResize:0,onResize:({target:o,width:r,height:n,delta:i})=>{i[0]&&(o.style.width=`${r}px`),i[1]&&(o.style.height=`${n}px`);},onResizeEnd:()=>{e();},scalable:true,throttleScale:0,renderDirections:["w","e"],onScale:({target:o,transform:r})=>{o.style.transform=r;}})};var F=Node.create({name:"math",inline:true,group:"inline",atom:true,selectable:true,marks:"",addAttributes(){return {latex:""}},addOptions(){return {shouldRender:(t,e)=>{let o=t.doc.resolve(e);return o.parent.isTextblock?o.parent.type.name!=="codeBlock":false},katexOptions:{throwOnError:false},HTMLAttributes:{}}},addCommands(){return {setLatex:({latex:t})=>({chain:e,state:o})=>{if(!t)return  false;let{from:r,to:n,$anchor:i}=o.selection;return this.options.shouldRender(o,i.pos)?e().insertContentAt({from:r,to:n},{type:"math",attrs:{latex:t}}).setTextSelection({from:r,to:r+1}).run():false},unsetLatex:()=>({editor:t,state:e,chain:o})=>{let r=t.getAttributes(this.name).latex;if(typeof r!="string")return  false;let{from:n,to:i}=e.selection;return o().command(({tr:a})=>(a.insertText(r,n,i),true)).setTextSelection({from:n,to:n+r.length}).run()}}},parseHTML(){return [{tag:`span[data-type="${this.name}"]`}]},renderHTML({node:t,HTMLAttributes:e}){let o=t.attrs.latex??"";return ["span",mergeAttributes(e,{"data-type":this.name}),o]},renderText({node:t}){return t.attrs.latex??""},addNodeView(){return ({node:t,HTMLAttributes:e,getPos:o,editor:r})=>{let n=document.createElement("span"),i=t.attrs.latex??"";return Object.entries(this.options.HTMLAttributes).forEach(([a,s])=>{n.setAttribute(a,s);}),Object.entries(e).forEach(([a,s])=>{n.setAttribute(a,s);}),n.addEventListener("click",a=>{if(r.isEditable&&typeof o=="function"){let s=o(),d=t.nodeSize;r.commands.setTextSelection({from:s,to:s+d});}}),n.contentEditable="false",n.innerHTML=Dt.renderToString(i,this.options.katexOptions),{dom:n}}}});var Wt=/(https?:\/\/)?(www\.)?x\.com\/([a-zA-Z0-9_]{1,15})(\/status\/(\d+))?(\/\S*)?/g,_t=/^https?:\/\/(www\.)?x\.com\/([a-zA-Z0-9_]{1,15})(\/status\/(\d+))?(\/\S*)?$/,qt=t=>t.match(_t),Gt=({node:t})=>{let o=t?.attrs?.src?.split("/").pop();return o?jsx(NodeViewWrapper,{children:jsx("div",{"data-twitter":"",children:jsx(Tweet,{id:o})})}):null},$=Node.create({name:"twitter",addOptions(){return {addPasteHandler:true,HTMLAttributes:{},inline:false,origin:""}},addNodeView(){return ReactNodeViewRenderer(Gt,{attrs:this.options.HTMLAttributes})},inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:true,addAttributes(){return {src:{default:null}}},parseHTML(){return [{tag:"div[data-twitter]"}]},addCommands(){return {setTweet:t=>({commands:e})=>qt(t.src)?e.insertContent({type:this.name,attrs:t}):false}},addPasteRules(){return this.options.addPasteHandler?[nodePasteRule({find:Wt,type:this.type,getAttributes:t=>({src:t.input})})]:[]},renderHTML({HTMLAttributes:t}){return ["div",mergeAttributes({"data-twitter":""},t)]}});var Xt=Jt.extend({name:"image",addAttributes(){return {...this.parent?.(),width:{default:null},height:{default:null}}}}),V=Xt;var te=/(?:^|\s)((?:==)((?:[^~=]+))(?:==))$/,ee=/(?:^|\s)((?:==)((?:[^~=]+))(?:==))/g,oe=Mark.create({name:"ai-highlight",addOptions(){return {HTMLAttributes:{}}},addAttributes(){return {color:{default:null,parseHTML:t=>t.getAttribute("data-color")||t.style.backgroundColor,renderHTML:t=>t.color?{"data-color":t.color,style:`background-color: ${t.color}; color: inherit`}:{}}}},parseHTML(){return [{tag:"mark"}]},renderHTML({HTMLAttributes:t}){return ["mark",mergeAttributes(this.options.HTMLAttributes,t),0]},addCommands(){return {setAIHighlight:t=>({commands:e})=>e.setMark(this.name,t),toggleAIHighlight:t=>({commands:e})=>e.toggleMark(this.name,t),unsetAIHighlight:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return {"Mod-Shift-h":()=>this.editor.commands.toggleAIHighlight()}},addInputRules(){return [markInputRule({find:te,type:this.type})]},addPasteRules(){return [markPasteRule({find:ee,type:this.type})]}}),re=t=>{let e=t.state.tr;e.removeMark(0,t.state.doc.nodeSize-2,t.state.schema.marks["ai-highlight"]),t.view.dispatch(e);},ne=(t,e)=>{t.chain().setAIHighlight({color:e??"#c1ecf970"}).run();};var me=Extension.create({name:"slash-command",addOptions(){return {suggestion:{char:"/",command:({editor:t,range:e,props:o})=>{o.command({editor:t,range:e});}}}},addProseMirrorPlugins(){return [se({editor:this.editor,...this.options.suggestion})]}}),le=t=>{let e=null,o=null;return {onStart:r=>{e=new ReactRenderer(k,{props:r,editor:r.editor});let{selection:n}=r.editor.state;if(n.$from.node(n.$from.depth).type.name==="codeBlock")return  false;o=de("body",{getReferenceClientRect:r.clientRect,appendTo:()=>t?t.current:document.body,content:e.element,showOnCreate:true,interactive:true,trigger:"manual",placement:"bottom-start"});},onUpdate:r=>{e?.updateProps(r),o?.[0]?.setProps({getReferenceClientRect:r.clientRect});},onKeyDown:r=>r.event.key==="Escape"?(o?.[0]?.hide(),true):e?.ref?.onKeyDown(r),onExit:()=>{o?.[0]?.destroy(),e?.destroy();}}},pe=t=>t,ce=t=>{if(["ArrowUp","ArrowDown","Enter"].includes(t.key)&&document.querySelector("#slash-command"))return  true};var Ae=Ee.configure({placeholder:({node:t})=>t.type.name==="heading"?`Heading ${t.attrs.level}`:"Press '/' for commands",includeChildren:true}),ve=fe.configure({multicolor:true}),He=ge.extend({addInputRules(){return [new InputRule({find:/^(?:---|—-|___\s|\*\*\*\s)$/u,handler:({state:t,range:e})=>{let o={},{tr:r}=t,n=e.from,i=e.to;r.insert(n-1,this.type.create(o)).delete(r.mapping.map(n),r.mapping.map(i));}})]}});var g=new PluginKey("upload-image"),_=({imageClass:t})=>new Plugin({key:g,state:{init(){return DecorationSet.empty},apply(e,o){o=o.map(e.mapping,e.doc);let r=e.getMeta(this);if(r?.add){let{id:n,pos:i,src:a}=r.add,s=document.createElement("div");s.setAttribute("class","img-placeholder");let d=document.createElement("img");d.setAttribute("class",t),d.src=a,s.appendChild(d);let m=Decoration.widget(i+1,s,{id:n});o=o.add(e.doc,[m]);}else r?.remove&&(o=o.remove(o.find(undefined,undefined,n=>n.id==r.remove.id)));return o}},props:{decorations(e){return this.getState(e)}}});function Oe(t,e){let r=g.getState(t).find(undefined,undefined,n=>n.id==e);return r.length?r[0]?.from:null}var q=({validateFn:t,onUpload:e})=>(o,r,n)=>{if(!t?.(o))return;let a={},s=r.state.tr;s.selection.empty||s.deleteSelection();let d=new FileReader;d.readAsDataURL(o),d.onload=()=>{s.setMeta(g,{add:{id:a,pos:n,src:d.result}}),r.dispatch(s);},e(o).then(m=>{let{schema:h}=r.state,c=Oe(r.state,a);if(c==null)return;let Y=typeof m=="object"?d.result:m,v=h.nodes.image?.create({src:Y});if(!v)return;let Q=r.state.tr.replaceWith(c,c,v).setMeta(g,{remove:{id:a}});r.dispatch(Q);},()=>{let m=r.state.tr.delete(n,n).setMeta(g,{remove:{id:a}});r.dispatch(m);});},G=(t,e,o)=>{if(e.clipboardData?.files.length){e.preventDefault();let[r]=Array.from(e.clipboardData.files),n=t.state.selection.from;return r&&o(r,t,n),true}return  false},J=(t,e,o,r)=>{if(!o&&e.dataTransfer?.files.length){e.preventDefault();let[n]=Array.from(e.dataTransfer.files),i=t.posAtCoords({left:e.clientX,top:e.clientY});return n&&r(n,t,i?.pos??-1),true}return  false};function X(t){try{return new URL(t),!0}catch{return  false}}function De(t){if(X(t))return t;try{if(t.includes(".")&&!t.includes(" "))return new URL(`https://${t}`).toString()}catch{return null}}var Be=(t,e)=>{let o=[];t.state.doc.forEach((i,a)=>a>=e?false:(o.push(i),true));let r=Fragment.fromArray(o),n=t.state.doc.copy(r);return t.storage.markdown.serializer.serialize(n)},ze=t=>{let e=t.state.doc.content,o=t.state.doc.copy(e);return t.storage.markdown.serializer.serialize(o)};
+export{oe as AIHighlight,me as Command,z as CustomKeymap,P as EditorBubble,w as EditorBubbleItem,T as EditorCommand,B as EditorCommandEmpty,S as EditorCommandItem,O as EditorCommandList,I as EditorContent,U as EditorRoot,ve as HighlightExtension,He as HorizontalRule,K as ImageResizer,F as Mathematics,Ae as Placeholder,V as UpdatedImage,_ as UploadImagesPlugin,ne as addAIHighlight,q as createImageUpload,pe as createSuggestionItems,ze as getAllContent,Be as getPrevText,De as getUrlFromString,ce as handleCommandNavigation,J as handleImageDrop,G as handleImagePaste,X as isValidUrl,E as queryAtom,u as rangeAtom,re as removeAIHighlight,le as renderItems};
