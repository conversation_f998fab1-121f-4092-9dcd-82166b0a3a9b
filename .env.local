VITE_PUBLIC_APP_URL=http://localhost:3000

# Supabase Configuration
VITE_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
VITE_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Legacy Backend URL (will be removed)
VITE_PUBLIC_BACKEND_URL=http://localhost:8787

# Legacy Database (will be migrated to Supabase)
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/zerodotemail"

# Legacy Auth (will be replaced with <PERSON>pabase Auth)
BETTER_AUTH_SECRET=my-better-auth-secret
BETTER_AUTH_URL=http://localhost:3000

COOKIE_DOMAIN="localhost"

# OAuth Configuration (will be configured in Supabase)
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Upstash/Local Redis Instance
REDIS_URL="http://localhost:8079"
REDIS_TOKEN="upstash-local-token"

# Resend API Key
RESEND_API_KEY=

# OpenAI API Key
OPENAI_API_KEY=
PERPLEXITY_API_KEY=

# OpenAI Model names (gpt-4o, gpt-4o-mini etc)
OPENAI_MODEL=
OPENAI_MINI_MODEL=

#AI PROMPT
AI_SYSTEM_PROMPT=""

NODE_ENV="development"

AUTUMN_SECRET_KEY=

TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=