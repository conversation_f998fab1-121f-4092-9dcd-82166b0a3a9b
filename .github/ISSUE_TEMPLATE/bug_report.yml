name: Bug Report
description: Report a bug to help us improve
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! Please provide as much detail as possible to help us resolve the issue quickly.

  - type: textarea
    id: bug-description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: When I try to..., the application...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: Steps to Reproduce
      description: Detailed steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: What actually happened? If applicable, add screenshots to help explain your problem.
    validations:
      required: true

  - type: dropdown
    id: browsers
    attributes:
      label: What browsers are you seeing the problem on?
      multiple: true
      options:
        - Chrome
        - Firefox
        - Safari
        - Microsoft Edge
        - Other

  - type: input
    id: version
    attributes:
      label: Application Version
      description: What version of the application are you running?
      placeholder: e.g., v1.0.0
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: Environment Information
      description: Please provide any relevant environment information
      placeholder: |
        - OS: [e.g., macOS 12.0]
        - Node.js version: [e.g., 16.x]
        - Browser version: [e.g., Chrome 96]

  - type: textarea
    id: logs
    attributes:
      label: Relevant Log Output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: shell

  - type: markdown
    attributes:
      value: |
        ---
        By submitting this bug report, you agree to follow our [Code of Conduct](../CODE_OF_CONDUCT.md) and understand that the information provided will be used to help resolve the issue.
