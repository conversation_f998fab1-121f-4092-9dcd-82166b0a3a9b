# Migration Guide: Backend to Supabase BaaS

This guide outlines the complete migration from the current Cloudflare Workers backend to Supabase Backend-as-a-Service for Vercel deployment.

## Overview

The migration involves:
- **Database**: PostgreSQL + Drizzle → Supabase PostgreSQL
- **Authentication**: Better Auth → Supabase Auth
- **API**: tRPC → Direct Supabase client calls
- **Real-time**: PartyKit → Supabase Realtime
- **Storage**: Cloudflare R2 → Supabase Storage
- **Background Jobs**: Cloudflare Queues → Supabase Edge Functions
- **Deployment**: Cloudflare Workers → Vercel Static/SSR

## Prerequisites

1. **Supabase Project Setup**
   ```bash
   # Install Supabase CLI
   npm install -g supabase

   # Create new project at https://supabase.com
   # Note down your project URL and anon key
   ```

2. **Environment Variables**
   ```bash
   # Add to your .env file
   VITE_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   VITE_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   ```

## Step 1: Database Migration

### 1.1 Export Current Data
```bash
# Export current PostgreSQL data
pg_dump $DATABASE_URL > backup.sql

# Or use Drizzle to export schema
pnpm db:generate
```

### 1.2 Create Supabase Schema
```bash
# Run the migration files
supabase db push

# Or manually run the SQL files:
# - supabase/migrations/001_initial_schema.sql
# - supabase/migrations/002_rls_policies.sql
```

### 1.3 Import Data
```bash
# Clean and import data to Supabase
# Note: You'll need to transform the data to match the new schema
```

## Step 2: Authentication Migration

### 2.1 Configure OAuth Providers
1. Go to Supabase Dashboard → Authentication → Providers
2. Enable Google OAuth:
   - Client ID: `your-google-client-id`
   - Client Secret: `your-google-client-secret`
   - Redirect URL: `https://your-project.supabase.co/auth/v1/callback`

3. Enable Azure (Microsoft) OAuth:
   - Client ID: `your-azure-client-id`
   - Client Secret: `your-azure-client-secret`
   - Redirect URL: `https://your-project.supabase.co/auth/v1/callback`

### 2.2 Update Frontend Auth
Replace Better Auth calls with Supabase Auth:

```typescript
// Old (Better Auth)
import { signIn, signOut } from '@/lib/auth-client';

// New (Supabase)
import { useSupabase } from '@/providers/supabase-provider';
const { signInWithGoogle, signOut } = useSupabase();
```

## Step 3: API Migration

### 3.1 Replace tRPC with Direct Calls
```typescript
// Old (tRPC)
const { data } = trpc.user.getProfile.useQuery();
const { mutate } = trpc.user.updateProfile.useMutation();

// New (Supabase + React Query)
const { data } = useUser();
const { mutate } = useUpdateUser();
```

### 3.2 Update Data Fetching
All tRPC procedures have been replaced with:
- `useUser()` - Get user profile
- `useConnections()` - Get email connections
- `useNotes(threadId)` - Get notes for thread
- `useUserSettings()` - Get user settings
- And corresponding mutation hooks

## Step 4: Real-time Features

### 4.1 Replace PartyKit with Supabase Realtime
```typescript
// Old (PartyKit)
import PartySocket from 'partysocket';

// New (Supabase Realtime)
import { supabase } from '@/lib/supabase';

const subscription = supabase
  .channel('notes')
  .on('postgres_changes', 
    { event: '*', schema: 'public', table: 'notes' },
    (payload) => {
      // Handle real-time updates
    }
  )
  .subscribe();
```

## Step 5: Background Processing

### 5.1 Deploy Edge Functions
```bash
# Deploy AI chat function
supabase functions deploy ai-chat

# Deploy email processor function
supabase functions deploy email-processor

# Set environment variables
supabase secrets set OPENAI_API_KEY=your-openai-key
```

### 5.2 Update Function Calls
```typescript
// Old (tRPC to Cloudflare Workers)
const response = await trpc.ai.chat.mutate({ messages });

// New (Direct Edge Function call)
const response = await supabase.functions.invoke('ai-chat', {
  body: { messages }
});
```

## Step 6: Storage Migration

### 6.1 Configure Supabase Storage
1. Go to Supabase Dashboard → Storage
2. Create buckets for file uploads
3. Set up storage policies

### 6.2 Update File Upload Logic
```typescript
// Old (Cloudflare R2)
// Upload logic using R2 API

// New (Supabase Storage)
const { data, error } = await supabase.storage
  .from('uploads')
  .upload('file-path', file);
```

## Step 7: Frontend Updates

### 7.1 Update Providers
```typescript
// Update apps/mail/providers/server-providers.tsx
import { SupabaseProvider } from './supabase-provider';

export function ServerProviders({ children }) {
  return (
    <SupabaseProvider>
      <QueryProvider>
        {children}
      </QueryProvider>
    </SupabaseProvider>
  );
}
```

### 7.2 Remove tRPC Dependencies
```bash
# Remove tRPC packages
pnpm remove @trpc/client @trpc/server @trpc/tanstack-react-query

# Remove server workspace dependency
# Update apps/mail/package.json to remove "@zero/server": "workspace:*"
```

## Step 8: Vercel Deployment

### 8.1 Configure Vercel Project
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to Vercel
vercel

# Set environment variables in Vercel dashboard:
# - VITE_PUBLIC_SUPABASE_URL
# - VITE_PUBLIC_SUPABASE_ANON_KEY
# - VITE_PUBLIC_APP_URL
```

### 8.2 Update Build Configuration
The `vercel.json` file is already configured for static deployment.

## Step 9: Testing & Validation

### 9.1 Functionality Testing
- [ ] User authentication (Google/Microsoft OAuth)
- [ ] Email connections management
- [ ] Notes CRUD operations
- [ ] Real-time updates
- [ ] AI chat functionality
- [ ] Email processing
- [ ] File uploads
- [ ] User settings

### 9.2 Performance Testing
- [ ] Page load times
- [ ] API response times
- [ ] Real-time latency
- [ ] Bundle size optimization

## Step 10: Cleanup

### 10.1 Remove Legacy Code
```bash
# Remove server app
rm -rf apps/server

# Remove Docker configurations
rm docker-compose.*.yaml
rm -rf docker/

# Update package.json scripts
# Remove backend-related scripts
```

### 10.2 Update Documentation
- Update README.md
- Update deployment instructions
- Update development setup guide

## Rollback Plan

If issues arise during migration:

1. **Database**: Restore from backup using `pg_restore`
2. **Frontend**: Revert to previous commit with tRPC
3. **Deployment**: Switch back to Cloudflare Workers
4. **DNS**: Update DNS records if needed

## Support & Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Check OAuth provider configuration
   - Verify redirect URLs
   - Check Supabase project settings

2. **Database Connection Issues**
   - Verify RLS policies
   - Check user permissions
   - Review database logs

3. **Real-time Not Working**
   - Check Supabase Realtime configuration
   - Verify subscription setup
   - Review browser console for errors

### Getting Help

- Supabase Documentation: https://supabase.com/docs
- Vercel Documentation: https://vercel.com/docs
- Project Issues: Create GitHub issue with detailed error information
