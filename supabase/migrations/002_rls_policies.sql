-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_hotkeys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.writing_style_matrix ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can delete own profile" ON public.users
  FOR DELETE USING (auth.uid() = id);

-- Connections policies
CREATE POLICY "Users can view own connections" ON public.connections
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own connections" ON public.connections
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own connections" ON public.connections
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own connections" ON public.connections
  FOR DELETE USING (auth.uid() = user_id);

-- Notes policies
CREATE POLICY "Users can view own notes" ON public.notes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own notes" ON public.notes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own notes" ON public.notes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own notes" ON public.notes
  FOR DELETE USING (auth.uid() = user_id);

-- Summaries policies (users can access summaries for their connections)
CREATE POLICY "Users can view summaries for own connections" ON public.summaries
  FOR SELECT USING (
    connection_id IN (
      SELECT id FROM public.connections WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert summaries for own connections" ON public.summaries
  FOR INSERT WITH CHECK (
    connection_id IN (
      SELECT id FROM public.connections WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update summaries for own connections" ON public.summaries
  FOR UPDATE USING (
    connection_id IN (
      SELECT id FROM public.connections WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete summaries for own connections" ON public.summaries
  FOR DELETE USING (
    connection_id IN (
      SELECT id FROM public.connections WHERE user_id = auth.uid()
    )
  );

-- User settings policies
CREATE POLICY "Users can view own settings" ON public.user_settings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own settings" ON public.user_settings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own settings" ON public.user_settings
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own settings" ON public.user_settings
  FOR DELETE USING (auth.uid() = user_id);

-- User hotkeys policies
CREATE POLICY "Users can view own hotkeys" ON public.user_hotkeys
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own hotkeys" ON public.user_hotkeys
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own hotkeys" ON public.user_hotkeys
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own hotkeys" ON public.user_hotkeys
  FOR DELETE USING (auth.uid() = user_id);

-- Writing style matrix policies (users can access matrix for their connections)
CREATE POLICY "Users can view writing style for own connections" ON public.writing_style_matrix
  FOR SELECT USING (
    connection_id IN (
      SELECT id FROM public.connections WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert writing style for own connections" ON public.writing_style_matrix
  FOR INSERT WITH CHECK (
    connection_id IN (
      SELECT id FROM public.connections WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update writing style for own connections" ON public.writing_style_matrix
  FOR UPDATE USING (
    connection_id IN (
      SELECT id FROM public.connections WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete writing style for own connections" ON public.writing_style_matrix
  FOR DELETE USING (
    connection_id IN (
      SELECT id FROM public.connections WHERE user_id = auth.uid()
    )
  );

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, name, email, email_verified, image)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.email),
    NEW.email,
    NEW.email_confirmed_at IS NOT NULL,
    NEW.raw_user_meta_data->>'avatar_url'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
