import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const googleAiApiKey = Deno.env.get('GOOGLE_AI_API_KEY')!;

interface EmailProcessingRequest {
  messageId: string;
  connectionId: string;
  emailContent: string;
  subject: string;
  sender: string;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get the authorization header
    const authorization = req.headers.get('Authorization');
    if (!authorization) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
      global: {
        headers: { authorization },
      },
    });

    // Get the user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { messageId, connectionId, emailContent, subject, sender }: EmailProcessingRequest = await req.json();

    // Verify the connection belongs to the user
    const { data: connection, error: connectionError } = await supabase
      .from('connections')
      .select('*')
      .eq('id', connectionId)
      .eq('user_id', user.id)
      .single();

    if (connectionError || !connection) {
      return new Response(
        JSON.stringify({ error: 'Connection not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Generate email summary using Google AI Studio
    const summaryResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${googleAiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `You are an AI assistant that summarizes emails. Provide a concise, helpful summary of the email content, highlighting key points, action items, and important information. Keep it under 200 words.

Subject: ${subject}
From: ${sender}

Email Content:
${emailContent}`
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          maxOutputTokens: 300,
        },
      }),
    });

    if (!summaryResponse.ok) {
      throw new Error(`Google AI API error: ${summaryResponse.statusText}`);
    }

    const summaryData = await summaryResponse.json();
    const summary = summaryData.candidates?.[0]?.content?.parts?.[0]?.text || 'Unable to generate summary';

    // Generate suggested reply
    const replyResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${googleAiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `You are an AI assistant that suggests email replies. Generate a professional, appropriate reply to the email. Keep it concise and helpful.

Subject: ${subject}
From: ${sender}

Email Content:
${emailContent}`
          }]
        }],
        generationConfig: {
          temperature: 0.5,
          maxOutputTokens: 200,
        },
      }),
    });

    let suggestedReply = null;
    if (replyResponse.ok) {
      const replyData = await replyResponse.json();
      suggestedReply = replyData.candidates?.[0]?.content?.parts?.[0]?.text || null;
    }

    // Store the summary in the database
    const { data: summaryRecord, error: summaryError } = await supabase
      .from('summaries')
      .upsert({
        message_id: messageId,
        connection_id: connectionId,
        content: summary,
        suggested_reply: suggestedReply,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (summaryError) {
      throw summaryError;
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        summary: summaryRecord,
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in email-processor function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
