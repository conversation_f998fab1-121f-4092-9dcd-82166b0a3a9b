import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY')!;

interface EmailProcessingRequest {
  messageId: string;
  connectionId: string;
  emailContent: string;
  subject: string;
  sender: string;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get the authorization header
    const authorization = req.headers.get('Authorization');
    if (!authorization) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
      global: {
        headers: { authorization },
      },
    });

    // Get the user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { messageId, connectionId, emailContent, subject, sender }: EmailProcessingRequest = await req.json();

    // Verify the connection belongs to the user
    const { data: connection, error: connectionError } = await supabase
      .from('connections')
      .select('*')
      .eq('id', connectionId)
      .eq('user_id', user.id)
      .single();

    if (connectionError || !connection) {
      return new Response(
        JSON.stringify({ error: 'Connection not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Generate email summary using OpenAI
    const summaryResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an AI assistant that summarizes emails. Provide a concise, helpful summary of the email content, highlighting key points, action items, and important information. Keep it under 200 words.',
          },
          {
            role: 'user',
            content: `Subject: ${subject}\nFrom: ${sender}\n\nEmail Content:\n${emailContent}`,
          },
        ],
        max_tokens: 300,
      }),
    });

    if (!summaryResponse.ok) {
      throw new Error(`OpenAI API error: ${summaryResponse.statusText}`);
    }

    const summaryData = await summaryResponse.json();
    const summary = summaryData.choices[0]?.message?.content || 'Unable to generate summary';

    // Generate suggested reply
    const replyResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an AI assistant that suggests email replies. Generate a professional, appropriate reply to the email. Keep it concise and helpful.',
          },
          {
            role: 'user',
            content: `Subject: ${subject}\nFrom: ${sender}\n\nEmail Content:\n${emailContent}`,
          },
        ],
        max_tokens: 200,
      }),
    });

    let suggestedReply = null;
    if (replyResponse.ok) {
      const replyData = await replyResponse.json();
      suggestedReply = replyData.choices[0]?.message?.content || null;
    }

    // Store the summary in the database
    const { data: summaryRecord, error: summaryError } = await supabase
      .from('summaries')
      .upsert({
        message_id: messageId,
        connection_id: connectionId,
        content: summary,
        suggested_reply: suggestedReply,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (summaryError) {
      throw summaryError;
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        summary: summaryRecord,
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in email-processor function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
