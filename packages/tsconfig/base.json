{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ESNext", "lib": ["EsNext"], "allowJs": true, "checkJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "verbatimModuleSyntax": true, "allowImportingTsExtensions": true, "allowArbitraryExtensions": true, "types": ["node"]}}